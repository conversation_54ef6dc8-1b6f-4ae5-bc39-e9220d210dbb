package com.midas.crm.controller;

import com.midas.crm.entity.Curso;
import com.midas.crm.entity.CursoUsuario;
import com.midas.crm.entity.DTO.curso.CursoDTO;
import com.midas.crm.entity.DTO.curso.CursoUsuarioDTO;
import com.midas.crm.entity.DTO.user.UserDTO;
import com.midas.crm.entity.DTO.user.UserPageDTO;
import com.midas.crm.entity.User;
import com.midas.crm.entity.DTO.curso.AsignacionMasivaResponseDTO;
import com.midas.crm.exceptions.MidasExceptions;
import com.midas.crm.mapper.CursoMapper;
import com.midas.crm.mapper.UserMapper;
import com.midas.crm.repository.CursoRepository;
import com.midas.crm.repository.CursoUsuarioRepository;
import com.midas.crm.repository.UserRepository;
import com.midas.crm.service.ProgresoUsuarioService;
import com.midas.crm.service.UserService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.GenericResponseConstants;
import com.midas.crm.utils.MidasErrorMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController
@RequestMapping("${api.route.cursos-usuarios}")
@RequiredArgsConstructor
public class CursoUsuarioController {

    private final CursoUsuarioRepository cursoUsuarioRepository;
    private final CursoRepository cursoRepository;
    private final UserRepository userRepository;
    private final UserService userService;
    private final SimpMessagingTemplate messagingTemplate;
    private final ProgresoUsuarioService progresoUsuarioService;

    /**
     * Obtiene todas las asignaciones de cursos a usuarios
     * Implementado con programación funcional
     */
    @GetMapping
    public ResponseEntity<GenericResponse<List<CursoUsuario>>> getAllCursosUsuarios() {
        return Optional.of(cursoUsuarioRepository.findAll())
                .map(cursosUsuarios -> ResponseEntity.ok(
                        new GenericResponse<>(GenericResponseConstants.SUCCESS, "Listado de asignaciones", cursosUsuarios)
                ))
                .orElse(ResponseEntity.ok(
                        new GenericResponse<>(GenericResponseConstants.SUCCESS, "No se encontraron asignaciones", new ArrayList<>())
                ));
    }

    /**
     * Obtiene los cursos asignados a un usuario por su ID
     * Implementado con programación funcional
     */
    @GetMapping("/usuario/{usuarioId}/cursos")
    public ResponseEntity<GenericResponse<List<CursoDTO>>> getCursosDetallesByUsuarioId(@PathVariable Long usuarioId) {
        // Verificar si el usuario existe
        if (!userRepository.existsById(usuarioId)) {
            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.ERROR, "Usuario no encontrado", new ArrayList<>())
            );
        }

        try {
            // Obtener los IDs de cursos asignados al usuario
            List<Long> cursosIds = cursoUsuarioRepository.findByUsuarioId(usuarioId).stream()
                    .map(cu -> cu.getCurso().getId())
                    .collect(Collectors.toList());

            // Si no hay cursos asignados, devolver lista vacía
            if (cursosIds.isEmpty()) {
                return ResponseEntity.ok(
                        new GenericResponse<>(GenericResponseConstants.SUCCESS, "No hay cursos asignados", new ArrayList<>())
                );
            }

            // Obtener los cursos y convertirlos a DTOs
            List<CursoDTO> cursosDTO = cursoRepository.findAllById(cursosIds).stream()
                    .map(CursoMapper::toDTO)
                    .collect(Collectors.toList());

            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.SUCCESS, "Cursos asignados al usuario", cursosDTO)
            );
        } catch (Exception e) {
            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.ERROR, "Error al obtener cursos asignados: " + e.getMessage(), new ArrayList<>())
            );
        }
    }

    /**
     * Obtiene las asignaciones de cursos para un usuario específico
     * Implementado con programación funcional
     * @param usuarioId ID del usuario
     */
    @GetMapping("/usuario/{usuarioId}")
    public ResponseEntity<GenericResponse<List<Map<String, Object>>>> getCursosByUsuarioId(@PathVariable Long usuarioId) {
        // Verificar si el usuario existe
        if (!userRepository.existsById(usuarioId)) {
            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.ERROR, "Usuario no encontrado", new ArrayList<>())
            );
        }

        try {
            // Obtener las asignaciones y transformarlas a mapas usando programación funcional
            List<Map<String, Object>> result = cursoUsuarioRepository.findByUsuarioId(usuarioId).stream()
                    .map(cu -> {
                        Map<String, Object> item = new HashMap<>();
                        item.put("id", cu.getId());
                        item.put("cursoId", cu.getCurso().getId());
                        item.put("usuarioId", cu.getUsuario().getId());
                        item.put("fechaAsignacion", cu.getFechaAsignacion());
                        item.put("estado", cu.getEstado());
                        item.put("completado", cu.isCompletado());
                        item.put("fechaCompletado", cu.getFechaCompletado());
                        item.put("porcentajeCompletado", cu.getPorcentajeCompletado());
                        item.put("ultimaVisualizacion", cu.getUltimaVisualizacion());
                        return item;
                    })
                    .collect(Collectors.toList());

            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.SUCCESS, "Cursos asignados al usuario", result)
            );
        } catch (Exception e) {
            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.ERROR, "Error al obtener cursos asignados: " + e.getMessage(), new ArrayList<>())
            );
        }
    }

    /**
     * Convierte una entidad CursoUsuario a DTO
     * Método de utilidad para la programación funcional
     */
    private CursoUsuarioDTO convertToDTO(CursoUsuario cursoUsuario) {
        CursoUsuarioDTO dto = new CursoUsuarioDTO();
        dto.setId(cursoUsuario.getId());
        dto.setCursoId(cursoUsuario.getCurso().getId());
        dto.setUsuarioId(cursoUsuario.getUsuario().getId());
        dto.setFechaAsignacion(cursoUsuario.getFechaAsignacion());
        dto.setEstado(cursoUsuario.getEstado());
        dto.setCompletado(cursoUsuario.isCompletado());
        dto.setFechaCompletado(cursoUsuario.getFechaCompletado());
        dto.setPorcentajeCompletado(cursoUsuario.getPorcentajeCompletado());
        dto.setUltimaVisualizacion(cursoUsuario.getUltimaVisualizacion());
        return dto;
    }

    /**
     * Obtiene los usuarios asignados a un curso específico con información de progreso
     * Implementado con programación funcional
     * @param cursoId ID del curso
     */
    @GetMapping("/curso/{cursoId}")
    public ResponseEntity<GenericResponse<List<Map<String, Object>>>> getUsuariosByCursoId(@PathVariable Long cursoId) {
        // Verificar si el curso existe
        if (!cursoRepository.existsById(cursoId)) {
            throw new MidasExceptions(MidasErrorMessage.CURSO_NOT_FOUND);
        }

        try {
            // Obtener las asignaciones con detalles
            List<CursoUsuario> cursosUsuarios = cursoUsuarioRepository.findByCursoIdWithDetails(cursoId);

            // Transformar las asignaciones a mapas con información de progreso
            List<Map<String, Object>> result = new ArrayList<>();

            for (CursoUsuario cu : cursosUsuarios) {
                Map<String, Object> item = new HashMap<>();

                // Información básica de la asignación
                item.put("id", cu.getId());
                item.put("curso", cu.getCurso());
                item.put("usuario", cu.getUsuario());
                item.put("fechaAsignacion", cu.getFechaAsignacion());
                item.put("estado", cu.getEstado());
                item.put("completado", cu.isCompletado());
                item.put("fechaCompletado", cu.getFechaCompletado());
                item.put("porcentajeCompletado", cu.getPorcentajeCompletado());
                item.put("ultimaVisualizacion", cu.getUltimaVisualizacion());

                // Obtener información de progreso
                try {
                    Map<String, Object> progreso = progresoUsuarioService.getResumenProgresoCurso(cursoId, cu.getUsuario().getId());
                    item.put("progreso", progreso);
                } catch (Exception e) {
                    // Si hay error al obtener el progreso, establecer valores por defecto
                    Map<String, Object> progresoDefault = new HashMap<>();
                    progresoDefault.put("totalLecciones", 0);
                    progresoDefault.put("leccionesCompletadas", 0);
                    progresoDefault.put("porcentajeProgreso", 0);
                    item.put("progreso", progresoDefault);
                }

                result.add(item);
            }

            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.SUCCESS, "Usuarios asignados al curso", result)
            );
        } catch (Exception e) {
            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.ERROR, "Error al obtener usuarios asignados: " + e.getMessage(), new ArrayList<>())
            );
        }
    }

    /**
     * Asigna un curso a un usuario
     * Implementado con programación funcional
     * @param request Datos de la asignación
     */
    @PostMapping
    public ResponseEntity<GenericResponse<CursoUsuario>> asignarCursoAUsuario(@RequestBody Map<String, Object> request) {
        try {
            // Extraer datos del request
            Long cursoId = Long.valueOf(request.get("cursoId").toString());
            Long usuarioId = Long.valueOf(request.get("usuarioId").toString());
            String estado = request.containsKey("estado") ? request.get("estado").toString() : "A";

            // Verificar que el curso y el usuario existen
            Curso curso = cursoRepository.findById(cursoId)
                    .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.CURSO_NOT_FOUND));

            User usuario = userRepository.findById(usuarioId)
                    .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.USUARIO_NOT_FOUND));

            // Verificar si ya existe la asignación
            if (cursoUsuarioRepository.existsByCursoIdAndUsuarioId(cursoId, usuarioId)) {
                throw new MidasExceptions(MidasErrorMessage.CURSO_USUARIO_ALREADY_EXISTS);
            }

            // Crear y guardar la asignación usando programación funcional
            return Optional.of(new CursoUsuario())
                    .map(cursoUsuario -> {
                        cursoUsuario.setCurso(curso);
                        cursoUsuario.setUsuario(usuario);
                        cursoUsuario.setFechaAsignacion(LocalDateTime.now());
                        cursoUsuario.setEstado(estado);
                        return cursoUsuarioRepository.save(cursoUsuario);
                    })
                    .map(savedCursoUsuario -> ResponseEntity.ok(
                            new GenericResponse<>(GenericResponseConstants.SUCCESS, "Curso asignado al usuario exitosamente", savedCursoUsuario)
                    ))
                    .orElse(ResponseEntity.badRequest().build());
        } catch (MidasExceptions e) {
            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.ERROR, e.getMessage(), null)
            );
        } catch (Exception e) {
            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.ERROR, "Error al asignar curso: " + e.getMessage(), null)
            );
        }
    }

    /**
     * Asigna un curso a múltiples usuarios
     * Implementado con programación funcional
     * @param request Datos de la asignación masiva
     */
    @PostMapping("/masivo")
    @Transactional
    public ResponseEntity<GenericResponse<AsignacionMasivaResponseDTO>> asignarCursoAUsuariosMasivo(@RequestBody Map<String, Object> request) {
        try {
            // Extraer datos del request
            Long cursoId = Long.valueOf(request.get("cursoId").toString());
            @SuppressWarnings("unchecked")
            List<Integer> usuarioIdsRaw = (List<Integer>) request.get("usuarioIds");
            List<Long> usuarioIds = usuarioIdsRaw.stream()
                    .map(Long::valueOf)
                    .collect(Collectors.toList());

            // Verificar que el curso existe
            Curso curso = cursoRepository.findById(cursoId)
                    .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.CURSO_NOT_FOUND));

            // Preparar la respuesta
            AsignacionMasivaResponseDTO response = new AsignacionMasivaResponseDTO();
            List<Long> usuariosAsignados = new ArrayList<>();
            List<String> errores = new ArrayList<>();

            // Procesar cada usuario usando programación funcional
            usuarioIds.forEach(usuarioId -> {
                try {
                    // Verificar que el usuario existe
                    if (!userRepository.existsById(usuarioId)) {
                        errores.add("Usuario con ID " + usuarioId + " no encontrado");
                        return;
                    }

                    // Verificar si ya existe la asignación
                    if (cursoUsuarioRepository.existsByCursoIdAndUsuarioId(cursoId, usuarioId)) {
                        // Actualizar la asignación existente a estado activo
                        Optional.ofNullable(cursoUsuarioRepository.findByCursoIdAndUsuarioId(cursoId, usuarioId))
                                .ifPresent(existente -> {
                                    existente.setEstado("A");
                                    cursoUsuarioRepository.save(existente);
                                    usuariosAsignados.add(usuarioId);
                                });
                        return;
                    }

                    // Crear la asignación usando programación funcional
                    userRepository.findById(usuarioId)
                            .ifPresent(usuario -> {
                                CursoUsuario cursoUsuario = new CursoUsuario();
                                cursoUsuario.setCurso(curso);
                                cursoUsuario.setUsuario(usuario);
                                cursoUsuario.setFechaAsignacion(LocalDateTime.now());
                                cursoUsuario.setEstado("A");
                                cursoUsuario.setCompletado(false);
                                cursoUsuario.setPorcentajeCompletado(0);
                                cursoUsuario.setUltimaVisualizacion(LocalDateTime.now());

                                cursoUsuarioRepository.save(cursoUsuario);
                                usuariosAsignados.add(usuarioId);
                            });
                } catch (Exception e) {
                    errores.add("Error al asignar usuario con ID " + usuarioId + ": " + e.getMessage());
                }
            });

            // Completar la respuesta
            response.setTotalAsignados(usuariosAsignados.size());
            response.setUsuariosAsignados(usuariosAsignados);
            response.setErrores(errores);

            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.SUCCESS, "Asignación masiva completada", response)
            );
        } catch (MidasExceptions e) {
            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.ERROR, e.getMessage(), null)
            );
        } catch (Exception e) {
            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.ERROR, "Error en asignación masiva: " + e.getMessage(), null)
            );
        }
    }

    /**
     * Actualiza una asignación de curso a usuario
     * Implementado con programación funcional
     * @param id ID de la asignación
     * @param request Datos de actualización (estado, completado, fechaCompletado, porcentajeCompletado, ultimaVisualizacion)
     */
    @PutMapping("/{id}")
    public ResponseEntity<GenericResponse<CursoUsuario>> updateCursoUsuario(@PathVariable Long id, @RequestBody Map<String, Object> request) {
        try {
            // Buscar la asignación y actualizarla usando programación funcional
            return cursoUsuarioRepository.findById(id)
                    .map(cursoUsuario -> {
                        // Actualizar estado
                        Optional.ofNullable(request.get("estado"))
                                .ifPresent(estado -> cursoUsuario.setEstado(estado.toString()));

                        // Actualizar completado
                        Optional.ofNullable(request.get("completado"))
                                .ifPresent(completado -> cursoUsuario.setCompletado(Boolean.parseBoolean(completado.toString())));

                        // Actualizar fechaCompletado
                        Optional.ofNullable(request.get("fechaCompletado"))
                                .map(Object::toString)
                                .filter(fechaStr -> !fechaStr.isEmpty())
                                .ifPresent(fechaStr -> cursoUsuario.setFechaCompletado(LocalDateTime.parse(fechaStr.replace("Z", ""))));

                        // Actualizar porcentajeCompletado
                        Optional.ofNullable(request.get("porcentajeCompletado"))
                                .ifPresent(porcentaje -> cursoUsuario.setPorcentajeCompletado(Integer.parseInt(porcentaje.toString())));

                        // Actualizar ultimaVisualizacion
                        Optional.ofNullable(request.get("ultimaVisualizacion"))
                                .map(Object::toString)
                                .filter(fechaStr -> !fechaStr.isEmpty())
                                .ifPresent(fechaStr -> cursoUsuario.setUltimaVisualizacion(LocalDateTime.parse(fechaStr.replace("Z", ""))));

                        // Guardar y devolver la asignación actualizada
                        return cursoUsuarioRepository.save(cursoUsuario);
                    })
                    .map(updatedCursoUsuario -> ResponseEntity.ok(
                            new GenericResponse<>(GenericResponseConstants.SUCCESS, "Asignación actualizada exitosamente", updatedCursoUsuario)
                    ))
                    .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.CURSO_USUARIO_NOT_FOUND));
        } catch (MidasExceptions e) {
            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.ERROR, e.getMessage(), null)
            );
        } catch (Exception e) {
            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.ERROR, "Error al actualizar asignación: " + e.getMessage(), null)
            );
        }
    }

    /**
     * Elimina una asignación de curso a usuario
     * Implementado con programación funcional
     * @param id ID de la asignación
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<GenericResponse<Void>> deleteCursoUsuario(@PathVariable Long id) {
        try {
            // Verificar si existe la asignación
            if (!cursoUsuarioRepository.existsById(id)) {
                throw new MidasExceptions(MidasErrorMessage.CURSO_USUARIO_NOT_FOUND);
            }

            // Eliminar la asignación
            cursoUsuarioRepository.deleteById(id);

            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.SUCCESS, "Asignación eliminada exitosamente", null)
            );
        } catch (MidasExceptions e) {
            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.ERROR, e.getMessage(), null)
            );
        } catch (Exception e) {
            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.ERROR, "Error al eliminar asignación: " + e.getMessage(), null)
            );
        }
    }

    /**
     * Elimina todas las asignaciones de un curso
     * Implementado con programación funcional
     * @param cursoId ID del curso
     */
    @DeleteMapping("/curso/{cursoId}")
    public ResponseEntity<GenericResponse<Void>> deleteAllByCursoId(@PathVariable Long cursoId) {
        try {
            // Verificar si existe el curso
            if (!cursoRepository.existsById(cursoId)) {
                throw new MidasExceptions(MidasErrorMessage.CURSO_NOT_FOUND);
            }

            // Eliminar todas las asignaciones del curso
            cursoUsuarioRepository.deleteByCursoId(cursoId);

            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.SUCCESS, "Asignaciones del curso eliminadas exitosamente", null)
            );
        } catch (MidasExceptions e) {
            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.ERROR, e.getMessage(), null)
            );
        } catch (Exception e) {
            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.ERROR, "Error al eliminar asignaciones: " + e.getMessage(), null)
            );
        }
    }

    /**
     * Obtiene los usuarios disponibles para asignar a un curso específico
     * (usuarios que no están asignados al curso)
     * @param cursoId ID del curso
     * @param page Número de página
     * @param size Tamaño de la página
     * @param query Término de búsqueda (opcional)
     */
    @GetMapping("/curso/{cursoId}/usuarios-disponibles")
    public ResponseEntity<GenericResponse<Map<String, Object>>> getUsuariosDisponiblesParaCurso(
            @PathVariable Long cursoId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String query) {
        try {
            // Verificar si el curso existe
            if (!cursoRepository.existsById(cursoId)) {
                throw new MidasExceptions(MidasErrorMessage.CURSO_NOT_FOUND);
            }

            // Obtener los IDs de usuarios ya asignados al curso
            List<Long> usuariosAsignadosIds = cursoUsuarioRepository.findByCursoId(cursoId).stream()
                    .map(cu -> cu.getUsuario().getId())
                    .collect(Collectors.toList());

            // Obtener todos los usuarios (con o sin búsqueda)
            List<UserDTO> allUsers;
            Map<String, Object> responseData = new HashMap<>();

            if (query != null && !query.trim().isEmpty()) {
                // Usar búsqueda
                try {
                    // Obtener usuarios mediante búsqueda
                    ResponseEntity<GenericResponse<Map<String, Object>>> searchResponse = userService.searchUsers(query, 0, Integer.MAX_VALUE, null);

                    // Verificar si la respuesta es válida
                    if (searchResponse.getBody() == null || searchResponse.getBody().getData() == null) {
                        return ResponseEntity.ok(
                                new GenericResponse<>(GenericResponseConstants.ERROR, "Error al obtener usuarios", null)
                        );
                    }

                    // Extraer usuarios de la respuesta
                    allUsers = (List<UserDTO>) searchResponse.getBody().getData().get("users");
                    responseData.put("totalItems", searchResponse.getBody().getData().get("totalItems"));
                    responseData.put("totalPages", searchResponse.getBody().getData().get("totalPages"));
                    responseData.put("currentPage", searchResponse.getBody().getData().get("currentPage"));
                } catch (Exception e) {
                    return ResponseEntity.ok(
                            new GenericResponse<>(GenericResponseConstants.ERROR, "Error al buscar usuarios: " + e.getMessage(), null)
                    );
                }
            } else {
                // Usar listado normal
                try {
                    // Obtener todos los usuarios
                    List<User> users = userRepository.findAll();

                    // Convertir a DTOs
                    allUsers = users.stream()
                            .map(UserMapper::toDTO)
                            .collect(Collectors.toList());

                    // Configurar información de paginación básica
                    responseData.put("totalItems", (long) allUsers.size());
                    responseData.put("totalPages", (long) Math.ceil((double) allUsers.size() / size));
                    responseData.put("currentPage", page);
                } catch (Exception e) {
                    return ResponseEntity.ok(
                            new GenericResponse<>(GenericResponseConstants.ERROR, "Error al listar usuarios: " + e.getMessage(), null)
                    );
                }
            }

            // Filtrar usuarios que no están asignados al curso
            List<UserDTO> usuariosDisponibles = allUsers.stream()
                    .filter(user -> !usuariosAsignadosIds.contains(user.getId()))
                    .collect(Collectors.toList());

            // Calcular el total de usuarios disponibles
            int totalDisponibles = usuariosDisponibles.size();

            // Calcular el número total de páginas
            int totalPages = (int) Math.ceil((double) totalDisponibles / size);

            // Aplicar paginación a la lista filtrada
            int fromIndex = page * size;
            int toIndex = Math.min(fromIndex + size, totalDisponibles);

            // Verificar que los índices sean válidos
            List<UserDTO> usuariosPaginados;
            if (fromIndex < totalDisponibles) {
                usuariosPaginados = usuariosDisponibles.subList(fromIndex, toIndex);
            } else {
                usuariosPaginados = new ArrayList<>();
            }

            // Crear y llenar el mapa de resultados
            Map<String, Object> result = new HashMap<>();
            result.put("users", usuariosPaginados);
            result.put("totalItems", totalDisponibles);
            result.put("totalPages", totalPages);
            result.put("currentPage", page);
            result.put("pageSize", size);

            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.SUCCESS, "Usuarios disponibles para el curso", result)
            );
        } catch (MidasExceptions e) {
            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.ERROR, e.getMessage(), null)
            );
        } catch (Exception e) {
            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.ERROR, "Error al obtener usuarios disponibles: " + e.getMessage(), null)
            );
        }
    }

    /**
     * Endpoint WebSocket para obtener usuarios disponibles para un curso
     */
    @MessageMapping("/cursos-usuarios.disponibles")
    @SendTo("/topic/cursos-usuarios/disponibles")
    public Map<String, Object> getUsuariosDisponiblesWs(Map<String, Object> payload) {
        try {
            // Extraer parámetros del payload
            Long cursoId = Long.valueOf(payload.get("cursoId").toString());
            int page = payload.containsKey("page") ? Integer.parseInt(payload.get("page").toString()) : 0;
            int size = payload.containsKey("size") ? Integer.parseInt(payload.get("size").toString()) : 10;
            String query = payload.containsKey("query") ? payload.get("query").toString() : null;

            // Obtener los IDs de usuarios ya asignados al curso
            List<Long> usuariosAsignadosIds = cursoUsuarioRepository.findByCursoId(cursoId).stream()
                    .map(cu -> cu.getUsuario().getId())
                    .collect(Collectors.toList());

            // Obtener usuarios paginados (con o sin búsqueda)
            List<UserDTO> allUsers;
            Map<String, Object> responseData = new HashMap<>();

            if (query != null && !query.trim().isEmpty()) {
                // Usar búsqueda
                try {
                    // Obtener usuarios mediante búsqueda
                    ResponseEntity<GenericResponse<Map<String, Object>>> searchResponse = userService.searchUsers(query, page, size, null);

                    // Verificar si la respuesta es válida
                    if (searchResponse.getBody() == null || searchResponse.getBody().getData() == null) {
                        Map<String, Object> errorResult = new HashMap<>();
                        errorResult.put("error", "Error al obtener usuarios");
                        errorResult.put("users", new ArrayList<>());
                        errorResult.put("totalItems", 0);
                        errorResult.put("totalPages", 0);
                        errorResult.put("currentPage", page);
                        errorResult.put("pageSize", size);
                        return errorResult;
                    }

                    // Extraer datos de la respuesta de búsqueda
                    Map<String, Object> searchData = searchResponse.getBody().getData();

                    // Extraer la lista de usuarios
                    @SuppressWarnings("unchecked")
                    List<UserDTO> searchUsers = (List<UserDTO>) searchData.get("users");
                    allUsers = searchUsers;

                    // Copiar información de paginación
                    responseData.put("totalItems", searchData.get("totalItems"));
                    responseData.put("totalPages", searchData.get("totalPages"));
                    responseData.put("currentPage", searchData.get("currentPage"));
                } catch (Exception e) {
                    Map<String, Object> errorResult = new HashMap<>();
                    errorResult.put("error", "Error al buscar usuarios: " + e.getMessage());
                    errorResult.put("users", new ArrayList<>());
                    errorResult.put("totalItems", 0);
                    errorResult.put("totalPages", 0);
                    errorResult.put("currentPage", page);
                    errorResult.put("pageSize", size);
                    return errorResult;
                }
            } else {
                // Usar listado normal
                try {
                    // Obtener todos los usuarios
                    List<User> users = userRepository.findAll();

                    // Convertir a DTOs
                    allUsers = users.stream()
                            .map(UserMapper::toDTO)
                            .collect(Collectors.toList());

                    // Configurar información de paginación básica
                    responseData.put("totalItems", (long) allUsers.size());
                    responseData.put("totalPages", 1);
                    responseData.put("currentPage", 0);
                } catch (Exception e) {
                    Map<String, Object> errorResult = new HashMap<>();
                    errorResult.put("error", "Error al listar usuarios: " + e.getMessage());
                    errorResult.put("users", new ArrayList<>());
                    errorResult.put("totalItems", 0);
                    errorResult.put("totalPages", 0);
                    errorResult.put("currentPage", page);
                    errorResult.put("pageSize", size);
                    return errorResult;
                }
            }

            // Filtrar usuarios que no están asignados al curso
            List<UserDTO> usuariosDisponibles = allUsers.stream()
                    .filter(user -> !usuariosAsignadosIds.contains(user.getId()))
                    .collect(Collectors.toList());

            // Crear y llenar el mapa de resultados
            Map<String, Object> result = new HashMap<>();
            result.put("users", usuariosDisponibles);
            result.put("totalItems", usuariosDisponibles.size());
            result.put("totalPages", responseData.get("totalPages"));
            result.put("currentPage", page);
            result.put("pageSize", size);
            result.put("cursoId", cursoId);

            return result;
        } catch (Exception e) {
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "Error: " + e.getMessage());
            errorResult.put("users", new ArrayList<>());
            errorResult.put("totalItems", 0);
            errorResult.put("totalPages", 0);
            errorResult.put("currentPage", 0);
            errorResult.put("pageSize", 10);
            return errorResult;
        }
    }
}
