package com.midas.crm.entity.DTO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * DTO para representar información de usuarios que han leído una notificación
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NotificationReadUserDTO {

    private Long userId;
    private String userName;
    private String userEmail;
    private LocalDateTime readAt;

    // Constructor para casos donde no tenemos fecha de lectura
    public NotificationReadUserDTO(Long userId, String userName, String userEmail) {
        this.userId = userId;
        this.userName = userName;
        this.userEmail = userEmail;
        this.readAt = LocalDateTime.now();
    }
}
