package com.midas.crm.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import java.time.LocalDateTime;

@Entity
@Table(name = "manuales")
@Data
@JsonInclude(JsonInclude.Include.ALWAYS)
@SQLDelete(sql = "UPDATE manuales SET deleted_at = CURRENT_TIMESTAMP, is_active = false WHERE id = ?")
@Where(clause = "deleted_at IS NULL")
public class Manual {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(nullable = false, length = 255)
    private String nombre;

    @Column(nullable = false, length = 2)
    private String tipo;

    @Column(length = 500)
    private String archivo;

    @Column(name = "is_active")
    private Boolean isActive = true;

    @Column(name = "user_create_id")
    private Long userCreateId;

    @Column(name = "user_update_id")
    private Long userUpdateId;

    @Column(name = "user_delete_id")
    private Long userDeleteId;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "deleted_at")
    private LocalDateTime deletedAt;

    @Column(name = "horario", length = 10)
    private String horario;

    @Transient
    private String tipoText;

    @PrePersist
    protected void onCreate() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now(); // Asegúrate de que esto esté presente
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = LocalDateTime.now();
    }

    public void setTipoTextFromCode() {
        switch (this.tipo != null ? this.tipo.toUpperCase() : "") {
            case "S" -> this.tipoText = "Manual de Software";
            case "B" -> this.tipoText = "Gestión de Backlog";
            case "M" -> this.tipoText = "Vodafone Micropyme";
            case "R" -> this.tipoText = "Vodafone Residencial";
            case "T" -> this.tipoText = "Tarifario";
            case "O" -> this.tipoText = "Otro";
            default -> this.tipoText = "";
        }
    }
}