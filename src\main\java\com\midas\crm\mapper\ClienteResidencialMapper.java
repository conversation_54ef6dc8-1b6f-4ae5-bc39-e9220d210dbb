package com.midas.crm.mapper;

import com.midas.crm.entity.ClienteResidencial;
import com.midas.crm.entity.DTO.cliente.ClienteResidencialDTO;

public class ClienteResidencialMapper {

    public static ClienteResidencialDTO toDTO(ClienteResidencial cliente) {
        ClienteResidencialDTO dto = new ClienteResidencialDTO();
        dto.setId(cliente.getId());
        dto.setCampania(cliente.getCampania());
        dto.setNombresApellidos(cliente.getNombresApellidos());
        dto.setNifNie(cliente.getNifNie());
        dto.setNacionalidad(cliente.getNacionalidad());
        dto.setFechaNacimiento(cliente.getFechaNacimiento());
        dto.setGenero(cliente.getGenero());
        dto.setCorreoElectronico(cliente.getCorreoElectronico());
        dto.setMovilContacto(cliente.getMovilContacto());
        dto.setDireccion(cliente.getDireccion());
        dto.setCodigoPostal(cliente.getCodigoPostal());
        dto.setProvincia(cliente.getProvincia());
        dto.setCiudad(cliente.getCiudad());
        dto.setTipoPlan(cliente.getTipoPlan());
        dto.setVentaRealizada(cliente.getVentaRealizada());
        dto.setDeseaPromocionesLowi(cliente.getDeseaPromocionesLowi());
        dto.setFechaCreacion(cliente.getFechaCreacion());
        dto.setMovilesAPortar(cliente.getMovilesAPortar());
        dto.setNumeroMoviles(cliente.getNumeroMoviles());
        return dto;
    }

    public static ClienteResidencial toEntity(ClienteResidencialDTO dto) {
        return ClienteResidencial.builder()
                .id(dto.getId())
                .campania(dto.getCampania())
                .nombresApellidos(dto.getNombresApellidos())
                .nifNie(dto.getNifNie())
                .nacionalidad(dto.getNacionalidad())
                .fechaNacimiento(dto.getFechaNacimiento())
                .genero(dto.getGenero())
                .correoElectronico(dto.getCorreoElectronico())
                .movilContacto(dto.getMovilContacto())
                .direccion(dto.getDireccion())
                .codigoPostal(dto.getCodigoPostal())
                .provincia(dto.getProvincia())
                .ciudad(dto.getCiudad())
                .tipoPlan(dto.getTipoPlan())
                .ventaRealizada(dto.getVentaRealizada())
                .deseaPromocionesLowi(dto.getDeseaPromocionesLowi())
                .fechaCreacion(dto.getFechaCreacion())
                .movilesAPortar(dto.getMovilesAPortar())
                .numeroMoviles(dto.getNumeroMoviles())
                .build();
    }
}
