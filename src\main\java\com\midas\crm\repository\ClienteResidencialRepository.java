package com.midas.crm.repository;

import com.midas.crm.entity.DTO.cliente.ClienteConUsuarioDTO;
import com.midas.crm.entity.DTO.EstadisticaSedeDTO;
import com.midas.crm.entity.ClienteResidencial;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface ClienteResidencialRepository extends JpaRepository<ClienteResidencial, Long> {

        @Query("SELECT new com.midas.crm.entity.DTO.cliente.ClienteConUsuarioDTO(" +
                "u.dni, CONCAT(u.nombre, ' ', u.apellido), cr.fechaCreacion, cr.movilContacto, " +
                "CASE WHEN u.coordinador IS NOT NULL THEN CONCAT(u.coordinador.nombre, ' ', u.coordinador.apellido) ELSE '' END) "
                +
                "FROM ClienteResidencial cr JOIN cr.usuario u LEFT JOIN u.coordinador c")
        Page<ClienteConUsuarioDTO> obtenerClientesConUsuario(Pageable pageable);

        // Consulta para cargar el cliente con usuario y coordinador de forma ansiosa
        // (EAGER)
        @Query("SELECT c FROM ClienteResidencial c JOIN FETCH c.usuario u LEFT JOIN FETCH u.coordinador WHERE c.movilContacto = :movilContacto")
        List<ClienteResidencial> findByMovilContacto(@Param("movilContacto") String movilContacto);

        @Query("SELECT new com.midas.crm.entity.DTO.cliente.ClienteConUsuarioDTO(" +
                "u.dni, CONCAT(u.nombre, ' ', u.apellido), cr.fechaCreacion, cr.movilContacto, " +
                "CASE WHEN u.coordinador IS NOT NULL THEN CONCAT(u.coordinador.nombre, ' ', u.coordinador.apellido) ELSE '' END) "
                +
                "FROM ClienteResidencial cr " +
                "JOIN cr.usuario u " +
                "LEFT JOIN u.coordinador c " +
                "WHERE (:dniAsesor IS NULL OR :dniAsesor = '' OR u.dni = :dniAsesor) " +
                "AND (COALESCE(:nombreAsesor, '') = '' OR CONCAT(u.nombre, ' ', u.apellido) LIKE CONCAT('%', :nombreAsesor, '%')) "
                +
                "AND (:numeroMovil IS NULL OR :numeroMovil = '' OR cr.movilContacto = :numeroMovil) " +
                "AND (:fecha IS NULL OR DATE(cr.fechaCreacion) = :fecha)")
        Page<ClienteConUsuarioDTO> obtenerClientesConUsuarioFiltrados(
                @Param("dniAsesor") String dniAsesor,
                @Param("nombreAsesor") String nombreAsesor,
                @Param("numeroMovil") String numeroMovil,
                @Param("fecha") LocalDate fecha,
                Pageable pageable);

        @Query("SELECT new com.midas.crm.entity.DTO.cliente.ClienteConUsuarioDTO(" +
                "u.dni, CONCAT(u.nombre, ' ', u.apellido), cr.fechaCreacion, cr.movilContacto, " +
                "CASE WHEN u.coordinador IS NOT NULL THEN CONCAT(u.coordinador.nombre, ' ', u.coordinador.apellido) ELSE '' END) "
                +
                "FROM ClienteResidencial cr " +
                "JOIN cr.usuario u " +
                "LEFT JOIN u.coordinador c " +
                "WHERE (:dniAsesor IS NULL OR :dniAsesor = '' OR u.dni = :dniAsesor) " +
                "AND (COALESCE(:nombreAsesor, '') = '' OR CONCAT(u.nombre, ' ', u.apellido) LIKE CONCAT('%', :nombreAsesor, '%')) "
                +
                "AND (:numeroMovil IS NULL OR :numeroMovil = '' OR cr.movilContacto = :numeroMovil) " +
                "AND DATE(cr.fechaCreacion) = CURRENT_DATE")
        Page<ClienteConUsuarioDTO> obtenerClientesConUsuarioFiltradosPorFechaActual(
                @Param("dniAsesor") String dniAsesor,
                @Param("nombreAsesor") String nombreAsesor,
                @Param("numeroMovil") String numeroMovil,
                Pageable pageable);

        @Query("SELECT new com.midas.crm.entity.DTO.cliente.ClienteConUsuarioDTO(" +
                "u.dni, CONCAT(u.nombre, ' ', u.apellido), cr.fechaCreacion, cr.movilContacto, " +
                "CASE WHEN u.coordinador IS NOT NULL THEN CONCAT(u.coordinador.nombre, ' ', u.coordinador.apellido) ELSE '' END) "
                +
                "FROM ClienteResidencial cr " +
                "JOIN cr.usuario u " +
                "LEFT JOIN u.coordinador c " +
                "WHERE (:dniAsesor IS NULL OR :dniAsesor = '' OR u.dni = :dniAsesor) " +
                "AND (COALESCE(:nombreAsesor, '') = '' OR " +
                "REPLACE(TRIM(CONCAT(u.nombre, ' ', u.apellido)), '  ', ' ') LIKE CONCAT('%', REPLACE(TRIM(:nombreAsesor), '  ', ' '), '%')) "
                +
                "AND (:numeroMovil IS NULL OR :numeroMovil = '' OR cr.movilContacto = :numeroMovil) " +
                "AND DATE(cr.fechaCreacion) BETWEEN :fechaInicio AND :fechaFin")
        Page<ClienteConUsuarioDTO> obtenerClientesConUsuarioFiltradosPorRango(
                @Param("dniAsesor") String dniAsesor,
                @Param("nombreAsesor") String nombreAsesor,
                @Param("numeroMovil") String numeroMovil,
                @Param("fechaInicio") LocalDate fechaInicio,
                @Param("fechaFin") LocalDate fechaFin,
                Pageable pageable);

        List<ClienteResidencial> findByFechaCreacionBetween(LocalDateTime start, LocalDateTime end);

        /**
         * Encuentra clientes por fecha de creación entre dos fechas con soporte para
         * paginación
         *
         * @param start    Fecha de inicio
         * @param end      Fecha de fin
         * @param pageable Objeto de paginación
         * @return Página de clientes residenciales
         */
        Page<ClienteResidencial> findByFechaCreacionBetween(LocalDateTime start, LocalDateTime end, Pageable pageable);

        /**
         * Encuentra todos los clientes residenciales asociados a un usuario (asesor)
         * específico
         *
         * @param usuarioId ID del usuario (asesor)
         * @return Lista de clientes residenciales
         */
        List<ClienteResidencial> findByUsuarioId(Long usuarioId);

        /**
         * Cuenta el número de clientes residenciales asociados a un usuario (asesor)
         * específico
         *
         * @param usuarioId ID del usuario (asesor)
         * @return Número de clientes
         */
        @Query("SELECT COUNT(c) FROM ClienteResidencial c WHERE c.usuario.id = :usuarioId")
        Long countClientesByUsuarioId(@Param("usuarioId") Long usuarioId);

        /**
         * Encuentra todos los clientes residenciales con venta realizada asociados a un
         * usuario (asesor)
         *
         * @param usuarioId ID del usuario (asesor)
         * @return Lista de clientes con venta realizada
         */
        @Query("SELECT c FROM ClienteResidencial c WHERE c.usuario.id = :usuarioId AND c.ventaRealizada = true")
        List<ClienteResidencial> findVentasRealizadasByUsuarioId(@Param("usuarioId") Long usuarioId);

        /**
         * Reemplaza el método buscarPorDniMovilYFechaEntre en tu
         * ClienteResidencialRepository
         * Ahora también permite buscar por nombre completo del usuario
         */
        @Query("SELECT DISTINCT c FROM ClienteResidencial c " +
                "JOIN FETCH c.usuario u " +
                "LEFT JOIN FETCH c.movilesAPortar " +
                "WHERE (u.dni = :dni OR (:nombreCompleto IS NOT NULL AND :nombreCompleto != '' AND CONCAT(u.nombre, ' ', u.apellido) LIKE CONCAT('%', :nombreCompleto, '%'))) "
                +
                "AND c.movilContacto = :movil " +
                "AND c.fechaCreacion BETWEEN :inicio AND :fin")
        List<ClienteResidencial> buscarPorDniMovilYFechaEntre(
                @Param("dni") String dni,
                @Param("nombreCompleto") String nombreCompleto,
                @Param("movil") String movil,
                @Param("inicio") LocalDateTime inicio,
                @Param("fin") LocalDateTime fin);

        @Query("""
                SELECT new com.midas.crm.entity.DTO.cliente.ClienteConUsuarioDTO(
                    c.usuario.dni,
                    CONCAT(c.usuario.nombre, ' ', c.usuario.apellido),
                    c.fechaCreacion,
                    c.movilContacto,
                    CASE WHEN c.usuario.coordinador IS NOT NULL THEN CONCAT(c.usuario.coordinador.nombre, ' ', c.usuario.coordinador.apellido) ELSE '' END
                )
                FROM ClienteResidencial c
                LEFT JOIN c.usuario.coordinador coord
                WHERE DATE(c.fechaCreacion) = :fecha
                AND c.usuario.id IN :idsAsesores
            """)
        List<ClienteConUsuarioDTO> findClientesConUsuarioPorFechaYAsesores(
                @Param("fecha") LocalDate fecha,
                @Param("idsAsesores") List<Long> idsAsesores);

        List<ClienteResidencial> findByMovilContactoIn(List<String> moviles);

        /**
         * Obtiene solo los IDs de todos los clientes residenciales
         *
         * @return Lista de IDs de clientes
         */
        @Query("SELECT c.id FROM ClienteResidencial c")
        List<Long> findAllIds();

        /**
         * Encuentra clientes por sus IDs
         *
         * @param ids Lista de IDs de clientes
         * @return Lista de clientes residenciales
         */
        List<ClienteResidencial> findAllByIdIn(List<Long> ids);

        /**
         * Consulta optimizada para obtener solo los datos básicos de los clientes por
         * fecha de creación
         * con un límite máximo de registros para mejorar el rendimiento
         *
         * @param inicio Fecha de inicio
         * @param fin    Fecha de fin
         * @param limite Límite máximo de registros a retornar
         * @return Lista de arrays de objetos con los datos básicos de los clientes
         */
        @Query(value = "SELECT c.campania, c.nombres_apellidos, c.nif_nie, c.nacionalidad, c.fecha_nacimiento, " +
                "c.genero, c.correo_electronico, c.cuenta_bancaria, c.direccion, c.tipo_tecnologia, " +
                "c.velocidad, c.movil_contacto, c.fijo_compania, CONCAT(u.nombre, ' ', u.apellido) as nombre_comercial, c.observacion, c.numero_moviles, "
                +
                "c.desea_promociones_lowi, c.autoriza_seguros, c.autoriza_energias, c.venta_realizada, c.fecha_creacion "
                +
                "FROM cliente_residencial c " +
                "LEFT JOIN usuarios u ON c.usuario_id = u.codi_usuario " +
                "WHERE c.fecha_creacion BETWEEN ?1 AND ?2 " +
                "ORDER BY c.fecha_creacion DESC " +
                "LIMIT ?3", nativeQuery = true)
        List<Object[]> findClientesBasicosByFechaCreacion(
                LocalDateTime inicio,
                LocalDateTime fin,
                int limite);

        /**
         * Consulta optimizada para obtener solo los datos básicos de los clientes por
         * rango de fechas de creación para exportación masiva
         * con un límite máximo de registros para mejorar el rendimiento
         *
         * @param fechaInicio Fecha de inicio
         * @param fechaFin    Fecha de fin
         * @param limite      Límite máximo de registros a retornar
         * @return Lista de arrays de objetos con los datos básicos de los clientes
         */
        @Query(value = "SELECT c.campania, c.nombres_apellidos, c.nif_nie, c.nacionalidad, c.fecha_nacimiento, " +
                "c.genero, c.correo_electronico, c.cuenta_bancaria, c.direccion, c.tipo_tecnologia, " +
                "c.velocidad, c.movil_contacto, c.fijo_compania, CONCAT(u.nombre, ' ', u.apellido) as nombre_comercial, c.observacion, c.numero_moviles, "
                +
                "c.desea_promociones_lowi, c.autoriza_seguros, c.autoriza_energias, c.venta_realizada, c.fecha_creacion "
                +
                "FROM cliente_residencial c " +
                "LEFT JOIN usuarios u ON c.usuario_id = u.codi_usuario " +
                "WHERE c.fecha_creacion BETWEEN ?1 AND ?2 " +
                "ORDER BY c.fecha_creacion DESC " +
                "LIMIT ?3", nativeQuery = true)
        List<Object[]> findClientesBasicosByRangoFechas(
                LocalDateTime fechaInicio,
                LocalDateTime fechaFin,
                int limite);

        /**
         * Consulta optimizada para obtener solo los datos básicos de los clientes por
         * rango de fechas de creación con paginación para exportación masiva
         *
         * @param fechaInicio Fecha de inicio
         * @param fechaFin    Fecha de fin
         * @param limite      Límite máximo de registros a retornar
         * @param offset      Número de registros a saltar
         * @return Lista de arrays de objetos con los datos básicos de los clientes
         */
        @Query(value = "SELECT c.id, c.campania, c.nombres_apellidos, c.nif_nie, c.nacionalidad, c.fecha_nacimiento, " +
                "c.genero, c.correo_electronico, c.cuenta_bancaria, c.direccion, c.tipo_tecnologia, " +
                "c.velocidad, c.movil_contacto, c.fijo_compania, CONCAT(u.nombre, ' ', u.apellido) as nombre_comercial, c.observacion, c.numero_moviles, "
                +
                "c.desea_promociones_lowi, c.autoriza_seguros, c.autoriza_energias, c.venta_realizada, c.fecha_creacion "
                +
                "FROM cliente_residencial c " +
                "LEFT JOIN usuarios u ON c.usuario_id = u.codi_usuario " +
                "WHERE c.fecha_creacion BETWEEN ?1 AND ?2 " +
                "ORDER BY c.fecha_creacion DESC " +
                "LIMIT ?3 OFFSET ?4", nativeQuery = true)
        List<Object[]> findClientesBasicosByRangoFechasWithOffset(
                LocalDateTime fechaInicio,
                LocalDateTime fechaFin,
                int limite,
                int offset);

        /**
         * Consulta optimizada para obtener solo los datos básicos de todos los clientes
         * con un límite máximo de registros para mejorar el rendimiento
         *
         * @param limite Límite máximo de registros a retornar
         * @return Lista de arrays de objetos con los datos básicos de los clientes
         */
        @Query(value = "SELECT c.campania, c.nombres_apellidos, c.nif_nie, c.nacionalidad, c.fecha_nacimiento, " +
                "c.genero, c.correo_electronico, c.cuenta_bancaria, c.direccion, c.tipo_tecnologia, " +
                "c.velocidad, c.movil_contacto, c.fijo_compania, CONCAT(u.nombre, ' ', u.apellido) as nombre_comercial, c.observacion, c.numero_moviles "
                +
                "FROM cliente_residencial c " +
                "LEFT JOIN usuarios u ON c.usuario_id = u.codi_usuario " +
                "ORDER BY c.id DESC " +
                "LIMIT ?1", nativeQuery = true)
        List<Object[]> findClientesBasicos(int limite);

        /**
         * Consulta optimizada para obtener datos básicos de clientes usando un cursor
         * basado en ID
         * para paginación eficiente con grandes volúmenes de datos
         *
         * @param lastId     ID del último cliente procesado (para continuar desde ahí)
         * @param tamanoLote Tamaño del lote a recuperar
         * @return Lista de arrays de objetos con los datos básicos de los clientes
         */
        @Query(value = "SELECT c.id, c.campania, c.nombres_apellidos, c.nif_nie, c.nacionalidad, c.fecha_nacimiento, " +
                "c.genero, c.correo_electronico, c.cuenta_bancaria, c.direccion, c.tipo_tecnologia, " +
                "c.velocidad, c.movil_contacto, c.fijo_compania, CONCAT(u.nombre, ' ', u.apellido) as nombre_comercial, c.observacion, c.numero_moviles "
                +
                "FROM cliente_residencial c " +
                "LEFT JOIN usuarios u ON c.usuario_id = u.codi_usuario " +
                "WHERE c.id > ?1 " +
                "ORDER BY c.id ASC " +
                "LIMIT ?2", nativeQuery = true)
        List<Object[]> findClientesBasicosByCursor(long lastId, int tamanoLote);

        /**
         * Consulta optimizada para obtener datos básicos de clientes usando offset para
         * paginación
         *
         * @param offset     Número de registros a saltar
         * @param tamanoLote Tamaño del lote a recuperar
         * @return Lista de arrays de objetos con los datos básicos de los clientes
         */
        @Query(value = "SELECT c.campania, c.nombres_apellidos, c.nif_nie, c.nacionalidad, c.fecha_nacimiento, " +
                "c.genero, c.correo_electronico, c.cuenta_bancaria, c.direccion, c.tipo_tecnologia, " +
                "c.velocidad, c.movil_contacto, c.fijo_compania, CONCAT(u.nombre, ' ', u.apellido) as nombre_comercial, c.observacion, c.numero_moviles "
                +
                "FROM cliente_residencial c " +
                "LEFT JOIN usuarios u ON c.usuario_id = u.codi_usuario " +
                "ORDER BY c.id ASC " +
                "LIMIT ?2 OFFSET ?1", nativeQuery = true)
        List<Object[]> findClientesBasicosPaginados(int offset, int tamanoLote);

        // ===== MÉTODOS PARA ESTADÍSTICAS POR SEDE =====

        /**
         * Obtiene estadísticas agrupadas por sede, supervisor y vendedor para una fecha
         * específica con paginación
         *
         * @param fecha    Fecha para filtrar
         * @param pageable Información de paginación
         * @return Página de estadísticas
         */
        @Query("""
            SELECT new com.midas.crm.entity.DTO.EstadisticaSedeDTO(
                COALESCE(s.nombre, 'Sin Sede'),
                COALESCE(CONCAT(coord.nombre, ' ', coord.apellido), 'Sin Supervisor'),
                CONCAT(u.nombre, ' ', u.apellido),
                COUNT(c.id),
                SUM(CASE WHEN c.autorizaSeguros = true THEN 1 ELSE 0 END),
                SUM(CASE WHEN c.autorizaEnergias = true THEN 1 ELSE 0 END),
                SUM(CASE WHEN c.deseaPromocionesLowi = true THEN 1 ELSE 0 END)
            )
            FROM ClienteResidencial c
            JOIN c.usuario u
            LEFT JOIN u.coordinador coord
            LEFT JOIN u.sede s
            WHERE DATE(c.fechaCreacion) = :fecha
            GROUP BY s.nombre, coord.nombre, coord.apellido, u.nombre, u.apellido
            """)
        Page<EstadisticaSedeDTO> obtenerEstadisticasPorFecha(@Param("fecha") LocalDate fecha, Pageable pageable);

        /**
         * Obtiene estadísticas agrupadas por sede, supervisor y vendedor para una fecha
         * específica con paginación y filtro de búsqueda por nombre de vendedor
         *
         * @param fecha            Fecha para filtrar
         * @param busquedaVendedor Término de búsqueda para el nombre del vendedor
         *                         (opcional)
         * @param pageable         Información de paginación
         * @return Página de estadísticas
         */
        @Query("""
            SELECT new com.midas.crm.entity.DTO.EstadisticaSedeDTO(
                COALESCE(s.nombre, 'Sin Sede'),
                COALESCE(CONCAT(coord.nombre, ' ', coord.apellido), 'Sin Supervisor'),
                CONCAT(u.nombre, ' ', u.apellido),
                COUNT(c.id),
                SUM(CASE WHEN c.autorizaSeguros = true THEN 1 ELSE 0 END),
                SUM(CASE WHEN c.autorizaEnergias = true THEN 1 ELSE 0 END),
                SUM(CASE WHEN c.deseaPromocionesLowi = true THEN 1 ELSE 0 END)
            )
            FROM ClienteResidencial c
            JOIN c.usuario u
            LEFT JOIN u.coordinador coord
            LEFT JOIN u.sede s
            WHERE DATE(c.fechaCreacion) = :fecha
            AND (:busquedaVendedor IS NULL OR :busquedaVendedor = '' OR
                 LOWER(REPLACE(TRIM(CONCAT(u.nombre, ' ', u.apellido)), '  ', ' ')) LIKE LOWER(CONCAT('%', REPLACE(:busquedaVendedor, '  ', ' '), '%')) OR
                 LOWER(TRIM(u.nombre)) LIKE LOWER(CONCAT('%', :busquedaVendedor, '%')) OR
                 LOWER(TRIM(u.apellido)) LIKE LOWER(CONCAT('%', :busquedaVendedor, '%')))
            GROUP BY s.nombre, coord.nombre, coord.apellido, u.nombre, u.apellido
            """)
        Page<EstadisticaSedeDTO> obtenerEstadisticasPorFechaConBusqueda(
                @Param("fecha") LocalDate fecha,
                @Param("busquedaVendedor") String busquedaVendedor,
                Pageable pageable);

        /**
         * Obtiene estadísticas agrupadas por sede, supervisor y vendedor para una sede
         * y fecha específica con paginación
         *
         * @param sedeId   ID de la sede
         * @param fecha    Fecha para filtrar
         * @param pageable Información de paginación
         * @return Página de estadísticas
         */
        @Query("""
            SELECT new com.midas.crm.entity.DTO.EstadisticaSedeDTO(
                s.nombre,
                COALESCE(CONCAT(coord.nombre, ' ', coord.apellido), 'Sin Supervisor'),
                CONCAT(u.nombre, ' ', u.apellido),
                COUNT(c.id),
                SUM(CASE WHEN c.autorizaSeguros = true THEN 1 ELSE 0 END),
                SUM(CASE WHEN c.autorizaEnergias = true THEN 1 ELSE 0 END),
                SUM(CASE WHEN c.deseaPromocionesLowi = true THEN 1 ELSE 0 END)
            )
            FROM ClienteResidencial c
            JOIN c.usuario u
            LEFT JOIN u.coordinador coord
            JOIN u.sede s
            WHERE s.id = :sedeId AND DATE(c.fechaCreacion) = :fecha
            GROUP BY s.nombre, coord.nombre, coord.apellido, u.nombre, u.apellido
            """)
        Page<EstadisticaSedeDTO> obtenerEstadisticasPorSedeYFechaPaginado(@Param("sedeId") Long sedeId,
                                                                          @Param("fecha") LocalDate fecha, Pageable pageable);

        /**
         * Obtiene estadísticas agrupadas por sede, supervisor y vendedor para una sede
         * y fecha específica con paginación y filtro de búsqueda por nombre de vendedor
         *
         * @param sedeId           ID de la sede
         * @param fecha            Fecha para filtrar
         * @param busquedaVendedor Término de búsqueda para el nombre del vendedor
         *                         (opcional)
         * @param pageable         Información de paginación
         * @return Página de estadísticas
         */
        @Query("""
            SELECT new com.midas.crm.entity.DTO.EstadisticaSedeDTO(
                s.nombre,
                COALESCE(CONCAT(coord.nombre, ' ', coord.apellido), 'Sin Supervisor'),
                CONCAT(u.nombre, ' ', u.apellido),
                COUNT(c.id),
                SUM(CASE WHEN c.autorizaSeguros = true THEN 1 ELSE 0 END),
                SUM(CASE WHEN c.autorizaEnergias = true THEN 1 ELSE 0 END),
                SUM(CASE WHEN c.deseaPromocionesLowi = true THEN 1 ELSE 0 END)
            )
            FROM ClienteResidencial c
            JOIN c.usuario u
            LEFT JOIN u.coordinador coord
            JOIN u.sede s
            WHERE s.id = :sedeId AND DATE(c.fechaCreacion) = :fecha
            AND (:busquedaVendedor IS NULL OR :busquedaVendedor = '' OR
                 LOWER(REPLACE(TRIM(CONCAT(u.nombre, ' ', u.apellido)), '  ', ' ')) LIKE LOWER(CONCAT('%', REPLACE(:busquedaVendedor, '  ', ' '), '%')) OR
                 LOWER(TRIM(u.nombre)) LIKE LOWER(CONCAT('%', :busquedaVendedor, '%')) OR
                 LOWER(TRIM(u.apellido)) LIKE LOWER(CONCAT('%', :busquedaVendedor, '%')))
            GROUP BY s.nombre, coord.nombre, coord.apellido, u.nombre, u.apellido
            """)
        Page<EstadisticaSedeDTO> obtenerEstadisticasPorSedeYFechaConBusquedaPaginado(
                @Param("sedeId") Long sedeId,
                @Param("fecha") LocalDate fecha,
                @Param("busquedaVendedor") String busquedaVendedor,
                Pageable pageable);

        /**
         * Obtiene estadísticas agrupadas por sede, supervisor y vendedor para una fecha
         * específica (sin paginación para compatibilidad)
         *
         * @param fecha Fecha para filtrar
         * @return Lista de estadísticas
         */
        @Query("""
            SELECT new com.midas.crm.entity.DTO.EstadisticaSedeDTO(
                COALESCE(s.nombre, 'Sin Sede'),
                COALESCE(CONCAT(coord.nombre, ' ', coord.apellido), 'Sin Supervisor'),
                CONCAT(u.nombre, ' ', u.apellido),
                COUNT(c.id),
                SUM(CASE WHEN c.autorizaSeguros = true THEN 1 ELSE 0 END),
                SUM(CASE WHEN c.autorizaEnergias = true THEN 1 ELSE 0 END),
                SUM(CASE WHEN c.deseaPromocionesLowi = true THEN 1 ELSE 0 END)
            )
            FROM ClienteResidencial c
            JOIN c.usuario u
            LEFT JOIN u.coordinador coord
            LEFT JOIN u.sede s
            WHERE DATE(c.fechaCreacion) = :fecha
            GROUP BY s.nombre, coord.nombre, coord.apellido, u.nombre, u.apellido
            ORDER BY s.nombre, coord.nombre, u.nombre
            """)
        List<EstadisticaSedeDTO> obtenerEstadisticasPorFechaSinPaginacion(@Param("fecha") LocalDate fecha);

        /**
         * Obtiene estadísticas agrupadas por sede, supervisor y vendedor para una sede
         * y fecha específica
         *
         * @param sedeId ID de la sede
         * @param fecha  Fecha para filtrar
         * @return Lista de estadísticas
         */
        @Query("""
            SELECT new com.midas.crm.entity.DTO.EstadisticaSedeDTO(
                s.nombre,
                COALESCE(CONCAT(coord.nombre, ' ', coord.apellido), 'Sin Supervisor'),
                CONCAT(u.nombre, ' ', u.apellido),
                COUNT(c.id),
                SUM(CASE WHEN c.autorizaSeguros = true THEN 1 ELSE 0 END),
                SUM(CASE WHEN c.autorizaEnergias = true THEN 1 ELSE 0 END),
                SUM(CASE WHEN c.deseaPromocionesLowi = true THEN 1 ELSE 0 END)
            )
            FROM ClienteResidencial c
            JOIN c.usuario u
            LEFT JOIN u.coordinador coord
            JOIN u.sede s
            WHERE s.id = :sedeId AND DATE(c.fechaCreacion) = :fecha
            GROUP BY s.nombre, coord.nombre, coord.apellido, u.nombre, u.apellido
            ORDER BY coord.nombre, u.nombre
            """)
        List<EstadisticaSedeDTO> obtenerEstadisticasPorSedeYFecha(@Param("sedeId") Long sedeId,
                                                                  @Param("fecha") LocalDate fecha);

        /**
         * Obtiene estadísticas resumidas por sede para una fecha específica
         *
         * @param fecha Fecha para filtrar
         * @return Lista de estadísticas resumidas
         */
        @Query("""
            SELECT new com.midas.crm.entity.DTO.EstadisticaSedeDTO(
                COALESCE(s.nombre, 'Sin Sede'),
                'RESUMEN',
                'TOTAL',
                COUNT(c.id),
                SUM(CASE WHEN c.autorizaSeguros = true THEN 1 ELSE 0 END),
                SUM(CASE WHEN c.autorizaEnergias = true THEN 1 ELSE 0 END),
                SUM(CASE WHEN c.deseaPromocionesLowi = true THEN 1 ELSE 0 END)
            )
            FROM ClienteResidencial c
            JOIN c.usuario u
            LEFT JOIN u.sede s
            WHERE DATE(c.fechaCreacion) = :fecha
            GROUP BY s.nombre
            ORDER BY s.nombre
            """)
        List<EstadisticaSedeDTO> obtenerResumenPorSedeYFecha(@Param("fecha") LocalDate fecha);

        /**
         * Obtiene estadísticas por rango de fechas para todas las sedes
         *
         * @param fechaInicio Fecha de inicio
         * @param fechaFin    Fecha de fin
         * @return Lista de estadísticas
         */
        @Query("""
            SELECT new com.midas.crm.entity.DTO.EstadisticaSedeDTO(
                COALESCE(s.nombre, 'Sin Sede'),
                COALESCE(CONCAT(coord.nombre, ' ', coord.apellido), 'Sin Supervisor'),
                CONCAT(u.nombre, ' ', u.apellido),
                COUNT(c.id),
                SUM(CASE WHEN c.autorizaSeguros = true THEN 1 ELSE 0 END),
                SUM(CASE WHEN c.autorizaEnergias = true THEN 1 ELSE 0 END),
                SUM(CASE WHEN c.deseaPromocionesLowi = true THEN 1 ELSE 0 END)
            )
            FROM ClienteResidencial c
            JOIN c.usuario u
            LEFT JOIN u.coordinador coord
            LEFT JOIN u.sede s
            WHERE DATE(c.fechaCreacion) BETWEEN :fechaInicio AND :fechaFin
            GROUP BY s.nombre, coord.nombre, coord.apellido, u.nombre, u.apellido
            ORDER BY s.nombre, coord.nombre, u.nombre
            """)
        List<EstadisticaSedeDTO> obtenerEstadisticasPorRango(@Param("fechaInicio") LocalDate fechaInicio,
                                                             @Param("fechaFin") LocalDate fechaFin);

        /**
         * Obtiene estadísticas por rango de fechas para una sede específica
         *
         * @param sedeId      ID de la sede
         * @param fechaInicio Fecha de inicio
         * @param fechaFin    Fecha de fin
         * @return Lista de estadísticas
         */
        @Query("""
            SELECT new com.midas.crm.entity.DTO.EstadisticaSedeDTO(
                s.nombre,
                COALESCE(CONCAT(coord.nombre, ' ', coord.apellido), 'Sin Supervisor'),
                CONCAT(u.nombre, ' ', u.apellido),
                COUNT(c.id),
                SUM(CASE WHEN c.autorizaSeguros = true THEN 1 ELSE 0 END),
                SUM(CASE WHEN c.autorizaEnergias = true THEN 1 ELSE 0 END),
                SUM(CASE WHEN c.deseaPromocionesLowi = true THEN 1 ELSE 0 END)
            )
            FROM ClienteResidencial c
            JOIN c.usuario u
            LEFT JOIN u.coordinador coord
            JOIN u.sede s
            WHERE s.id = :sedeId AND DATE(c.fechaCreacion) BETWEEN :fechaInicio AND :fechaFin
            GROUP BY s.nombre, coord.nombre, coord.apellido, u.nombre, u.apellido
            ORDER BY coord.nombre, u.nombre
            """)
        List<EstadisticaSedeDTO> obtenerEstadisticasPorSedeYRango(@Param("sedeId") Long sedeId,
                                                                  @Param("fechaInicio") LocalDate fechaInicio, @Param("fechaFin") LocalDate fechaFin);

        /**
         * Obtiene estadísticas por rango de fechas para todas las sedes con búsqueda
         * por vendedor
         *
         * @param fechaInicio      Fecha de inicio
         * @param fechaFin         Fecha de fin
         * @param busquedaVendedor Término de búsqueda para el nombre del vendedor
         *                         (opcional)
         * @return Lista de estadísticas
         */
        @Query("""
            SELECT new com.midas.crm.entity.DTO.EstadisticaSedeDTO(
                COALESCE(s.nombre, 'Sin Sede'),
                COALESCE(CONCAT(coord.nombre, ' ', coord.apellido), 'Sin Supervisor'),
                CONCAT(u.nombre, ' ', u.apellido),
                COUNT(c.id),
                SUM(CASE WHEN c.autorizaSeguros = true THEN 1 ELSE 0 END),
                SUM(CASE WHEN c.autorizaEnergias = true THEN 1 ELSE 0 END),
                SUM(CASE WHEN c.deseaPromocionesLowi = true THEN 1 ELSE 0 END)
            )
            FROM ClienteResidencial c
            JOIN c.usuario u
            LEFT JOIN u.coordinador coord
            LEFT JOIN u.sede s
            WHERE DATE(c.fechaCreacion) BETWEEN :fechaInicio AND :fechaFin
            AND (:busquedaVendedor IS NULL OR :busquedaVendedor = '' OR
                 LOWER(REPLACE(TRIM(CONCAT(u.nombre, ' ', u.apellido)), '  ', ' ')) LIKE LOWER(CONCAT('%', REPLACE(:busquedaVendedor, '  ', ' '), '%')) OR
                 LOWER(TRIM(u.nombre)) LIKE LOWER(CONCAT('%', :busquedaVendedor, '%')) OR
                 LOWER(TRIM(u.apellido)) LIKE LOWER(CONCAT('%', :busquedaVendedor, '%')))
            GROUP BY s.nombre, coord.nombre, coord.apellido, u.nombre, u.apellido
            ORDER BY s.nombre, coord.nombre, u.nombre
            """)
        List<EstadisticaSedeDTO> obtenerEstadisticasPorRangoConBusqueda(
                @Param("fechaInicio") LocalDate fechaInicio,
                @Param("fechaFin") LocalDate fechaFin,
                @Param("busquedaVendedor") String busquedaVendedor);

        /**
         * Obtiene estadísticas por rango de fechas para una sede específica con
         * búsqueda por vendedor
         *
         * @param sedeId           ID de la sede
         * @param fechaInicio      Fecha de inicio
         * @param fechaFin         Fecha de fin
         * @param busquedaVendedor Término de búsqueda para el nombre del vendedor
         *                         (opcional)
         * @return Lista de estadísticas
         */
        @Query("""
            SELECT new com.midas.crm.entity.DTO.EstadisticaSedeDTO(
                s.nombre,
                COALESCE(CONCAT(coord.nombre, ' ', coord.apellido), 'Sin Supervisor'),
                CONCAT(u.nombre, ' ', u.apellido),
                COUNT(c.id),
                SUM(CASE WHEN c.autorizaSeguros = true THEN 1 ELSE 0 END),
                SUM(CASE WHEN c.autorizaEnergias = true THEN 1 ELSE 0 END),
                SUM(CASE WHEN c.deseaPromocionesLowi = true THEN 1 ELSE 0 END)
            )
            FROM ClienteResidencial c
            JOIN c.usuario u
            LEFT JOIN u.coordinador coord
            JOIN u.sede s
            WHERE s.id = :sedeId AND DATE(c.fechaCreacion) BETWEEN :fechaInicio AND :fechaFin
            AND (:busquedaVendedor IS NULL OR :busquedaVendedor = '' OR
                 LOWER(REPLACE(TRIM(CONCAT(u.nombre, ' ', u.apellido)), '  ', ' ')) LIKE LOWER(CONCAT('%', REPLACE(:busquedaVendedor, '  ', ' '), '%')) OR
                 LOWER(TRIM(u.nombre)) LIKE LOWER(CONCAT('%', :busquedaVendedor, '%')) OR
                 LOWER(TRIM(u.apellido)) LIKE LOWER(CONCAT('%', :busquedaVendedor, '%')))
            GROUP BY s.nombre, coord.nombre, coord.apellido, u.nombre, u.apellido
            ORDER BY coord.nombre, u.nombre
            """)
        List<EstadisticaSedeDTO> obtenerEstadisticasPorSedeYRangoConBusqueda(
                @Param("sedeId") Long sedeId,
                @Param("fechaInicio") LocalDate fechaInicio,
                @Param("fechaFin") LocalDate fechaFin,
                @Param("busquedaVendedor") String busquedaVendedor);

        // ===== MÉTODOS PAGINADOS =====

        /**
         * Obtiene estadísticas filtradas por sede y supervisor para una fecha
         * específica con paginación
         *
         * @param sedeId       ID de la sede
         * @param supervisorId ID del supervisor
         * @param fecha        Fecha para filtrar
         * @param pageable     Información de paginación
         * @return Página de estadísticas
         */
        @Query("""
            SELECT new com.midas.crm.entity.DTO.EstadisticaSedeDTO(
                s.nombre,
                CONCAT(coord.nombre, ' ', coord.apellido),
                CONCAT(u.nombre, ' ', u.apellido),
                COUNT(c.id),
                SUM(CASE WHEN c.autorizaSeguros = true THEN 1 ELSE 0 END),
                SUM(CASE WHEN c.autorizaEnergias = true THEN 1 ELSE 0 END),
                SUM(CASE WHEN c.deseaPromocionesLowi = true THEN 1 ELSE 0 END)
            )
            FROM ClienteResidencial c
            JOIN c.usuario u
            JOIN u.coordinador coord
            JOIN u.sede s
            WHERE s.id = :sedeId AND coord.id = :supervisorId AND DATE(c.fechaCreacion) = :fecha
            GROUP BY s.nombre, coord.nombre, coord.apellido, u.nombre, u.apellido
            """)
        Page<EstadisticaSedeDTO> obtenerEstadisticasPorSedeYSupervisorPaginado(@Param("sedeId") Long sedeId,
                                                                               @Param("supervisorId") Long supervisorId, @Param("fecha") LocalDate fecha, Pageable pageable);

        /**
         * Obtiene estadísticas filtradas por sede y supervisor para una fecha
         * específica con paginación y filtro de búsqueda por nombre de vendedor
         *
         * @param sedeId           ID de la sede
         * @param supervisorId     ID del supervisor
         * @param fecha            Fecha para filtrar
         * @param busquedaVendedor Término de búsqueda para el nombre del vendedor
         *                         (opcional)
         * @param pageable         Información de paginación
         * @return Página de estadísticas
         */
        @Query("""
            SELECT new com.midas.crm.entity.DTO.EstadisticaSedeDTO(
                s.nombre,
                CONCAT(coord.nombre, ' ', coord.apellido),
                CONCAT(u.nombre, ' ', u.apellido),
                COUNT(c.id),
                SUM(CASE WHEN c.autorizaSeguros = true THEN 1 ELSE 0 END),
                SUM(CASE WHEN c.autorizaEnergias = true THEN 1 ELSE 0 END),
                SUM(CASE WHEN c.deseaPromocionesLowi = true THEN 1 ELSE 0 END)
            )
            FROM ClienteResidencial c
            JOIN c.usuario u
            JOIN u.coordinador coord
            JOIN u.sede s
            WHERE s.id = :sedeId AND coord.id = :supervisorId AND DATE(c.fechaCreacion) = :fecha
            AND (:busquedaVendedor IS NULL OR :busquedaVendedor = '' OR
                 LOWER(REPLACE(TRIM(CONCAT(u.nombre, ' ', u.apellido)), '  ', ' ')) LIKE LOWER(CONCAT('%', REPLACE(:busquedaVendedor, '  ', ' '), '%')) OR
                 LOWER(TRIM(u.nombre)) LIKE LOWER(CONCAT('%', :busquedaVendedor, '%')) OR
                 LOWER(TRIM(u.apellido)) LIKE LOWER(CONCAT('%', :busquedaVendedor, '%')))
            GROUP BY s.nombre, coord.nombre, coord.apellido, u.nombre, u.apellido
            """)
        Page<EstadisticaSedeDTO> obtenerEstadisticasPorSedeYSupervisorConBusquedaPaginado(
                @Param("sedeId") Long sedeId,
                @Param("supervisorId") Long supervisorId,
                @Param("fecha") LocalDate fecha,
                @Param("busquedaVendedor") String busquedaVendedor,
                Pageable pageable);

        /**
         * Obtiene estadísticas filtradas solo por supervisor para una fecha específica
         * con paginación
         *
         * @param supervisorId ID del supervisor
         * @param fecha        Fecha para filtrar
         * @param pageable     Información de paginación
         * @return Página de estadísticas
         */
        @Query("""
            SELECT new com.midas.crm.entity.DTO.EstadisticaSedeDTO(
                COALESCE(s.nombre, 'Sin Sede'),
                CONCAT(coord.nombre, ' ', coord.apellido),
                CONCAT(u.nombre, ' ', u.apellido),
                COUNT(c.id),
                SUM(CASE WHEN c.autorizaSeguros = true THEN 1 ELSE 0 END),
                SUM(CASE WHEN c.autorizaEnergias = true THEN 1 ELSE 0 END),
                SUM(CASE WHEN c.deseaPromocionesLowi = true THEN 1 ELSE 0 END)
            )
            FROM ClienteResidencial c
            JOIN c.usuario u
            JOIN u.coordinador coord
            LEFT JOIN u.sede s
            WHERE coord.id = :supervisorId AND DATE(c.fechaCreacion) = :fecha
            GROUP BY s.nombre, coord.nombre, coord.apellido, u.nombre, u.apellido
            """)
        Page<EstadisticaSedeDTO> obtenerEstadisticasPorSupervisorPaginado(@Param("supervisorId") Long supervisorId,
                                                                          @Param("fecha") LocalDate fecha, Pageable pageable);

        /**
         * Obtiene estadísticas filtradas solo por supervisor para una fecha específica
         * con paginación y filtro de búsqueda por nombre de vendedor
         *
         * @param supervisorId     ID del supervisor
         * @param fecha            Fecha para filtrar
         * @param busquedaVendedor Término de búsqueda para el nombre del vendedor
         *                         (opcional)
         * @param pageable         Información de paginación
         * @return Página de estadísticas
         */
        @Query("""
            SELECT new com.midas.crm.entity.DTO.EstadisticaSedeDTO(
                COALESCE(s.nombre, 'Sin Sede'),
                CONCAT(coord.nombre, ' ', coord.apellido),
                CONCAT(u.nombre, ' ', u.apellido),
                COUNT(c.id),
                SUM(CASE WHEN c.autorizaSeguros = true THEN 1 ELSE 0 END),
                SUM(CASE WHEN c.autorizaEnergias = true THEN 1 ELSE 0 END),
                SUM(CASE WHEN c.deseaPromocionesLowi = true THEN 1 ELSE 0 END)
            )
            FROM ClienteResidencial c
            JOIN c.usuario u
            JOIN u.coordinador coord
            LEFT JOIN u.sede s
            WHERE coord.id = :supervisorId AND DATE(c.fechaCreacion) = :fecha
            AND (:busquedaVendedor IS NULL OR :busquedaVendedor = '' OR
                 LOWER(REPLACE(TRIM(CONCAT(u.nombre, ' ', u.apellido)), '  ', ' ')) LIKE LOWER(CONCAT('%', REPLACE(:busquedaVendedor, '  ', ' '), '%')) OR
                 LOWER(TRIM(u.nombre)) LIKE LOWER(CONCAT('%', :busquedaVendedor, '%')) OR
                 LOWER(TRIM(u.apellido)) LIKE LOWER(CONCAT('%', :busquedaVendedor, '%')))
            GROUP BY s.nombre, coord.nombre, coord.apellido, u.nombre, u.apellido
            """)
        Page<EstadisticaSedeDTO> obtenerEstadisticasPorSupervisorConBusquedaPaginado(
                @Param("supervisorId") Long supervisorId,
                @Param("fecha") LocalDate fecha,
                @Param("busquedaVendedor") String busquedaVendedor,
                Pageable pageable);

        // ===== CONSULTAS OPTIMIZADAS PARA EXPORTACIÓN RÁPIDA =====

        /**
         * Consulta optimizada para exportar clientes por sede y fecha - UNA SOLA
         * CONSULTA
         * Similar al método rápido de rango de fechas
         *
         * @param sedeId ID de la sede
         * @param fecha  Fecha específica
         * @param limite Límite máximo de registros
         * @return Lista de arrays de objetos con los datos básicos de los clientes
         */
        @Query(value = "SELECT c.id, c.campania, c.nombres_apellidos, c.nif_nie, c.nacionalidad, c.fecha_nacimiento, " +
                "c.genero, c.correo_electronico, c.cuenta_bancaria, c.direccion, c.tipo_tecnologia, " +
                "c.velocidad, c.movil_contacto, c.fijo_compania, CONCAT(u.nombre, ' ', u.apellido) as nombre_comercial, c.observacion, c.numero_moviles, "
                +
                "c.desea_promociones_lowi, c.autoriza_seguros, c.autoriza_energias, c.venta_realizada, c.fecha_creacion, "
                +
                "COALESCE(CONCAT(coord.nombre, ' ', coord.apellido), 'Sin Supervisor') as coordinador " +
                "FROM cliente_residencial c " +
                "LEFT JOIN usuarios u ON c.usuario_id = u.codi_usuario " +
                "LEFT JOIN usuarios coord ON u.coordinador_id = coord.codi_usuario " +
                "WHERE DATE(c.fecha_creacion) = ?2 " +
                "AND u.sede_id = ?1 " +
                "ORDER BY c.fecha_creacion DESC " +
                "LIMIT ?3", nativeQuery = true)
        List<Object[]> findClientesBasicosPorSedeYFechaOptimizado(
                Long sedeId,
                LocalDate fecha,
                int limite);

        /**
         * Consulta optimizada para exportar clientes por sede, supervisor y fecha - UNA
         * SOLA CONSULTA
         *
         * @param sedeId       ID de la sede
         * @param supervisorId ID del supervisor/coordinador
         * @param fecha        Fecha específica
         * @param limite       Límite máximo de registros
         * @return Lista de arrays de objetos con los datos básicos de los clientes
         */
        @Query(value = "SELECT c.id, c.campania, c.nombres_apellidos, c.nif_nie, c.nacionalidad, c.fecha_nacimiento, " +
                "c.genero, c.correo_electronico, c.cuenta_bancaria, c.direccion, c.tipo_tecnologia, " +
                "c.velocidad, c.movil_contacto, c.fijo_compania, CONCAT(u.nombre, ' ', u.apellido) as nombre_comercial, c.observacion, c.numero_moviles, "
                +
                "c.desea_promociones_lowi, c.autoriza_seguros, c.autoriza_energias, c.venta_realizada, c.fecha_creacion, "
                +
                "CONCAT(coord.nombre, ' ', coord.apellido) as coordinador " +
                "FROM cliente_residencial c " +
                "LEFT JOIN usuarios u ON c.usuario_id = u.codi_usuario " +
                "LEFT JOIN usuarios coord ON u.coordinador_id = coord.codi_usuario " +
                "WHERE DATE(c.fecha_creacion) = ?3 " +
                "AND u.sede_id = ?1 " +
                "AND u.coordinador_id = ?2 " +
                "ORDER BY c.fecha_creacion DESC " +
                "LIMIT ?4", nativeQuery = true)
        List<Object[]> findClientesBasicosPorSedeYSupervisorYFechaOptimizado(
                Long sedeId,
                Long supervisorId,
                LocalDate fecha,
                int limite);

        // ===== CONSULTAS OPTIMIZADAS PARA RANGO DE FECHAS =====

        /**
         * Consulta optimizada para exportar clientes por sede y rango de fechas
         *
         * @param sedeId      ID de la sede
         * @param fechaInicio Fecha de inicio del rango
         * @param fechaFin    Fecha de fin del rango
         * @param limite      Límite máximo de registros
         * @return Lista de arrays de objetos con los datos básicos de los clientes
         */
        @Query(value = "SELECT c.id, c.campania, c.nombres_apellidos, c.nif_nie, c.nacionalidad, c.fecha_nacimiento, " +
                "c.genero, c.correo_electronico, c.cuenta_bancaria, c.direccion, c.tipo_tecnologia, " +
                "c.velocidad, c.movil_contacto, c.fijo_compania, CONCAT(u.nombre, ' ', u.apellido) as nombre_comercial, c.observacion, c.numero_moviles, "
                +
                "c.desea_promociones_lowi, c.autoriza_seguros, c.autoriza_energias, c.venta_realizada, c.fecha_creacion, "
                +
                "COALESCE(CONCAT(coord.nombre, ' ', coord.apellido), 'Sin Supervisor') as coordinador " +
                "FROM cliente_residencial c " +
                "LEFT JOIN usuarios u ON c.usuario_id = u.codi_usuario " +
                "LEFT JOIN usuarios coord ON u.coordinador_id = coord.codi_usuario " +
                "WHERE c.fecha_creacion BETWEEN ?2 AND ?3 " +
                "AND u.sede_id = ?1 " +
                "ORDER BY c.fecha_creacion DESC " +
                "LIMIT ?4", nativeQuery = true)
        List<Object[]> findClientesBasicosPorSedeYRangoFechas(
                Long sedeId,
                LocalDateTime fechaInicio,
                LocalDateTime fechaFin,
                int limite);

        /**
         * Consulta optimizada para exportar clientes por sede, supervisor y rango de
         * fechas
         *
         * @param sedeId       ID de la sede
         * @param supervisorId ID del supervisor/coordinador
         * @param fechaInicio  Fecha de inicio del rango
         * @param fechaFin     Fecha de fin del rango
         * @param limite       Límite máximo de registros
         * @return Lista de arrays de objetos con los datos básicos de los clientes
         */
        @Query(value = "SELECT c.id, c.campania, c.nombres_apellidos, c.nif_nie, c.nacionalidad, c.fecha_nacimiento, " +
                "c.genero, c.correo_electronico, c.cuenta_bancaria, c.direccion, c.tipo_tecnologia, " +
                "c.velocidad, c.movil_contacto, c.fijo_compania, CONCAT(u.nombre, ' ', u.apellido) as nombre_comercial, c.observacion, c.numero_moviles, "
                +
                "c.desea_promociones_lowi, c.autoriza_seguros, c.autoriza_energias, c.venta_realizada, c.fecha_creacion, "
                +
                "CONCAT(coord.nombre, ' ', coord.apellido) as coordinador " +
                "FROM cliente_residencial c " +
                "LEFT JOIN usuarios u ON c.usuario_id = u.codi_usuario " +
                "LEFT JOIN usuarios coord ON u.coordinador_id = coord.codi_usuario " +
                "WHERE c.fecha_creacion BETWEEN ?3 AND ?4 " +
                "AND u.sede_id = ?1 " +
                "AND u.coordinador_id = ?2 " +
                "ORDER BY c.fecha_creacion DESC " +
                "LIMIT ?5", nativeQuery = true)
        List<Object[]> findClientesBasicosPorSedeYSupervisorYRangoFechas(
                Long sedeId,
                Long supervisorId,
                LocalDateTime fechaInicio,
                LocalDateTime fechaFin,
                int limite);

        /**
         * Consulta mejorada para obtener datos básicos de clientes por rango de fechas
         * con paginación - INCLUYE TODOS LOS CAMPOS NECESARIOS PARA CONSISTENCIA
         *
         * @param fechaInicio Fecha de inicio
         * @param fechaFin    Fecha de fin
         * @param limite      Límite máximo de registros a retornar
         * @param offset      Número de registros a saltar
         * @return Lista de arrays de objetos con los datos básicos de los clientes
         */
        @Query(value = "SELECT c.id, c.campania, c.nombres_apellidos, c.nif_nie, c.nacionalidad, c.fecha_nacimiento, " +
                "c.genero, c.correo_electronico, c.cuenta_bancaria, c.direccion, c.tipo_tecnologia, " +
                "c.velocidad, c.movil_contacto, c.fijo_compania, CONCAT(u.nombre, ' ', u.apellido) as nombre_comercial, c.observacion, c.numero_moviles, "
                +
                "c.desea_promociones_lowi, c.autoriza_seguros, c.autoriza_energias, c.venta_realizada, c.fecha_creacion, "
                +
                "COALESCE(CONCAT(coord.nombre, ' ', coord.apellido), 'Sin Supervisor') as coordinador " +
                "FROM cliente_residencial c " +
                "LEFT JOIN usuarios u ON c.usuario_id = u.codi_usuario " +
                "LEFT JOIN usuarios coord ON u.coordinador_id = coord.codi_usuario " +
                "WHERE c.fecha_creacion BETWEEN ?1 AND ?2 " +
                "ORDER BY c.fecha_creacion DESC " +
                "LIMIT ?3 OFFSET ?4", nativeQuery = true)
        List<Object[]> findClientesBasicosByRangoFechasWithOffsetMejorado(
                LocalDateTime fechaInicio,
                LocalDateTime fechaFin,
                int limite,
                int offset);

        /**
         * Consulta optimizada para exportar clientes por supervisor y rango de fechas
         *
         * @param supervisorId ID del supervisor/coordinador
         * @param fechaInicio  Fecha de inicio del rango
         * @param fechaFin     Fecha de fin del rango
         * @param limite       Límite máximo de registros
         * @return Lista de arrays de objetos con los datos básicos de los clientes
         */
        @Query(value = "SELECT c.id, c.campania, c.nombres_apellidos, c.nif_nie, c.nacionalidad, c.fecha_nacimiento, " +
                "c.genero, c.correo_electronico, c.cuenta_bancaria, c.direccion, c.tipo_tecnologia, " +
                "c.velocidad, c.movil_contacto, c.fijo_compania, CONCAT(u.nombre, ' ', u.apellido) as nombre_comercial, c.observacion, c.numero_moviles, "
                +
                "c.desea_promociones_lowi, c.autoriza_seguros, c.autoriza_energias, c.venta_realizada, c.fecha_creacion, "
                +
                "CONCAT(coord.nombre, ' ', coord.apellido) as coordinador " +
                "FROM cliente_residencial c " +
                "LEFT JOIN usuarios u ON c.usuario_id = u.codi_usuario " +
                "LEFT JOIN usuarios coord ON u.coordinador_id = coord.codi_usuario " +
                "WHERE c.fecha_creacion BETWEEN ?2 AND ?3 " +
                "AND u.coordinador_id = ?1 " +
                "ORDER BY c.fecha_creacion DESC " +
                "LIMIT ?4", nativeQuery = true)
        List<Object[]> findClientesBasicosPorSupervisorYRangoFechas(
                Long supervisorId,
                LocalDateTime fechaInicio,
                LocalDateTime fechaFin,
                int limite);

        // ===== CONSULTA OPTIMIZADA CON FILTRO DE BÚSQUEDA POR VENDEDOR =====

        /**
         * Consulta optimizada para obtener clientes con filtros completos incluyendo
         * búsqueda por vendedor
         * Soporta tanto fecha específica como rango de fechas
         */
        @Query(value = "SELECT c.id, c.campania, c.nombres_apellidos, c.nif_nie, c.nacionalidad, c.fecha_nacimiento, " +
                "c.genero, c.correo_electronico, c.cuenta_bancaria, c.direccion, c.tipo_tecnologia, " +
                "c.velocidad, c.movil_contacto, c.fijo_compania, CONCAT(u.nombre, ' ', u.apellido) as nombre_comercial, c.observacion, c.numero_moviles, "
                +
                "c.desea_promociones_lowi, c.autoriza_seguros, c.autoriza_energias, c.venta_realizada, c.fecha_creacion, "
                +
                "c.permanencia, c.plan_actual, c.tipo_plan, c.icc, c.codigo_postal, c.provincia, c.ciudad, c.numero_agente, "
                +
                "GROUP_CONCAT(DISTINCT map.movil SEPARATOR ', ') as moviles_a_portar, " +
                "COALESCE(CONCAT(coord.nombre, ' ', coord.apellido), 'Sin Supervisor') as coordinador " +
                "FROM cliente_residencial c " +
                "LEFT JOIN usuarios u ON c.usuario_id = u.codi_usuario " +
                "LEFT JOIN usuarios coord ON u.coordinador_id = coord.codi_usuario " +
                "LEFT JOIN cliente_residencial_moviles_aportar map ON c.id = map.cliente_residencial_id " +
                "WHERE (" +
                "  (?3 IS NOT NULL AND DATE(c.fecha_creacion) = ?3) OR " +
                "  (?4 IS NOT NULL AND ?5 IS NOT NULL AND c.fecha_creacion BETWEEN ?4 AND ?5)" +
                ") " +
                "AND (?1 IS NULL OR u.sede_id = ?1) " +
                "AND (?2 IS NULL OR u.coordinador_id = ?2) " +
                "AND (?6 IS NULL OR ?6 = '' OR " +
                "     LOWER(REPLACE(TRIM(CONCAT(u.nombre, ' ', u.apellido)), '  ', ' ')) LIKE LOWER(CONCAT('%', REPLACE(?6, '  ', ' '), '%')) OR "
                +
                "     LOWER(TRIM(u.nombre)) LIKE LOWER(CONCAT('%', ?6, '%')) OR " +
                "     LOWER(TRIM(u.apellido)) LIKE LOWER(CONCAT('%', ?6, '%'))" +
                ") " +
                "GROUP BY c.id, c.campania, c.nombres_apellidos, c.nif_nie, c.nacionalidad, c.fecha_nacimiento, " +
                "c.genero, c.correo_electronico, c.cuenta_bancaria, c.direccion, c.tipo_tecnologia, " +
                "c.velocidad, c.movil_contacto, c.fijo_compania, u.nombre, u.apellido, c.observacion, c.numero_moviles, " +
                "c.desea_promociones_lowi, c.autoriza_seguros, c.autoriza_energias, c.venta_realizada, c.fecha_creacion, " +
                "c.permanencia, c.plan_actual, c.tipo_plan, c.icc, c.codigo_postal, c.provincia, c.ciudad, c.numero_agente, "
                +
                "coord.nombre, coord.apellido " +
                "ORDER BY c.fecha_creacion DESC " +
                "LIMIT ?7", nativeQuery = true)
        List<Object[]> findClientesBasicosPorFiltrosConBusquedaVendedor(
                Long sedeId,
                Long supervisorId,
                LocalDate fecha,
                LocalDateTime fechaInicio,
                LocalDateTime fechaFin,
                String busquedaVendedor,
                int limite);

}