package com.midas.crm.repository;

import com.midas.crm.entity.NotificationRead;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repositorio para gestionar las operaciones de base de datos con la entidad
 * NotificationRead
 */
@Repository
public interface NotificationReadRepository extends JpaRepository<NotificationRead, Long> {

    /**
     * Busca un registro de notificación leída por usuario y notificación
     *
     * @param userId         ID del usuario
     * @param notificationId ID de la notificación
     * @return Registro de notificación leída (opcional)
     */
    Optional<NotificationRead> findByUserIdAndNotificationId(Long userId, Long notificationId);

    /**
     * Busca todos los registros de notificaciones leídas para un usuario
     *
     * @param userId ID del usuario
     * @return Lista de registros de notificaciones leídas
     */
    List<NotificationRead> findByUserId(Long userId);

    /**
     * Busca todos los registros de notificaciones leídas para una notificación
     *
     * @param notificationId ID de la notificación
     * @return Lista de registros de notificaciones leídas
     */
    List<NotificationRead> findByNotificationId(Long notificationId);

    /**
     * Verifica si existe un registro de notificación leída para un usuario y
     * notificación
     *
     * @param userId         ID del usuario
     * @param notificationId ID de la notificación
     * @return true si existe, false en caso contrario
     */
    boolean existsByUserIdAndNotificationId(Long userId, Long notificationId);

    /**
     * Elimina todos los registros de notificaciones leídas para un usuario
     *
     * @param userId ID del usuario
     */
    void deleteByUserId(Long userId);

    /**
     * Elimina todos los registros de notificaciones leídas para una notificación
     *
     * @param notificationId ID de la notificación
     */
    void deleteByNotificationId(Long notificationId);

    /**
     * Obtiene información de usuarios que han leído una notificación específica
     *
     * @param notificationId ID de la notificación
     * @return Lista de información de usuarios que han leído la notificación
     */
    @Query("SELECT nr.userId, u.nombre, u.email, nr.readAt " +
            "FROM NotificationRead nr " +
            "JOIN User u ON nr.userId = u.id " +
            "WHERE nr.notificationId = :notificationId " +
            "ORDER BY nr.readAt DESC")
    List<Object[]> findUsersWhoReadNotification(@Param("notificationId") Long notificationId);

    /**
     * Cuenta cuántos usuarios han leído una notificación específica
     *
     * @param notificationId ID de la notificación
     * @return Número de usuarios que han leído la notificación
     */
    @Query("SELECT COUNT(nr) FROM NotificationRead nr WHERE nr.notificationId = :notificationId")
    long countUsersWhoReadNotification(@Param("notificationId") Long notificationId);
}
