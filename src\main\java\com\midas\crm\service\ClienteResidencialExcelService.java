package com.midas.crm.service;

import com.midas.crm.entity.DTO.cliente.ClienteConUsuarioDTO;
import com.midas.crm.entity.ClienteResidencial;
import com.midas.crm.entity.User;
import com.midas.crm.repository.ClienteResidencialRepository;
import com.midas.crm.repository.UserRepository;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Service
public class ClienteResidencialExcelService {

    @Autowired
    private ClienteResidencialRepository clienteResidencialRepository;

    @Autowired
    private UserRepository userRepository;

    /**
     * =========================================================================
     * 1) MÉTODO PARA EXPORTAR "MASIVO"
     * Cada cliente en una fila y cada columna para un campo específico.
     * Ejemplo de columnas: NÚMERO (movilContacto), CAMPAÑA, NOMBRE...
     *
     * Versión ultra-optimizada para servidor con 188 GB de RAM
     * Procesamiento directo en memoria para máxima velocidad
     * =========================================================================
     */
    public byte[] generarExcelClientesMasivo() {
        // Constantes para controlar el rendimiento - optimizadas para 188 GB de RAM
        final int TAMANO_LOTE = 10000; // Tamaño del lote aumentado para aprovechar la RAM
        final int WINDOW_SIZE = 1000; // Número de filas mantenidas en memoria aumentado

        try (SXSSFWorkbook workbook = new SXSSFWorkbook(WINDOW_SIZE)) {
            // Configurar SXSSFWorkbook para máximo rendimiento
            workbook.setCompressTempFiles(false); // No comprimir archivos temporales para mayor velocidad

            Sheet sheet = workbook.createSheet("Clientes");

            // Definir encabezados básicos (solo los campos esenciales)
            String[] headers = {
                    "CAMPAÑA", "NOMBRES", "NIF/NIE", "NACIONALIDAD", "NACIMIENTO",
                    "GÉNERO", "EMAIL", "CUENTA", "DIRECCIÓN", "TECNOLOGIA",
                    "VELOCIDAD", "MOVIL", "FIJO", "COMERCIAL", "OBSERVACIONES", "NÚMERO MÓVILES",
                    "DESEA PROMOCIONES LOWI"
            };

            // Crear encabezados sin estilos complejos
            Row headerRow = sheet.createRow(0);
            for (int i = 0; i < headers.length; i++) {
                headerRow.createCell(i).setCellValue(headers[i]);
            }

            // Variables para el procesamiento por lotes
            int rowIndex = 1;
            long lastId = 0;
            boolean hayMasDatos = true;

            // Procesar los datos en lotes usando un enfoque de cursor basado en ID
            while (hayMasDatos) {
                // Obtener un lote de datos usando ID como cursor para paginación eficiente
                List<Object[]> loteClientes = clienteResidencialRepository.findClientesBasicosByCursor(
                        lastId, TAMANO_LOTE);

                // Si no hay más datos, salir del bucle
                if (loteClientes.isEmpty()) {
                    hayMasDatos = false;
                    continue;
                }

                // Procesar cada registro en el lote actual - optimizado para velocidad
                for (Object[] datos : loteClientes) {
                    // Actualizar el ID del último registro procesado para la próxima iteración
                    if (datos[0] != null) {
                        lastId = Long.parseLong(datos[0].toString());
                    }

                    // Crear fila y llenar datos
                    Row dataRow = sheet.createRow(rowIndex++);

                    // Llenar solo las columnas esenciales (empezando desde el índice 1 para omitir
                    // el ID)
                    for (int i = 1; i < datos.length && (i - 1) < headers.length; i++) {
                        if (datos[i] != null) {
                            dataRow.createCell(i - 1).setCellValue(datos[i].toString());
                        } else {
                            dataRow.createCell(i - 1).setCellValue("");
                        }
                    }
                }
            }

            // Escribir directamente a un ByteArrayOutputStream para mayor velocidad
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream(1024 * 1024); // Preasignar 1MB
            workbook.write(outputStream);

            // Limpiar recursos temporales de SXSSF
            workbook.dispose();

            return outputStream.toByteArray();

        } catch (Exception e) {
            e.printStackTrace();
            return new byte[0];
        }
    }

    /**
     * Método auxiliar para llenar las celdas de un cliente en una fila
     * Evita la duplicación de código y mejora la mantenibilidad
     */
    private void llenarCeldasCliente(Row row, ClienteResidencial cliente) {
        row.createCell(0).setCellValue(cliente.getCampania() != null ? cliente.getCampania() : "");
        row.createCell(1).setCellValue(cliente.getNombresApellidos() != null ? cliente.getNombresApellidos() : "");
        row.createCell(2).setCellValue(cliente.getNifNie() != null ? cliente.getNifNie() : "");
        row.createCell(3).setCellValue(cliente.getNacionalidad() != null ? cliente.getNacionalidad() : "");
        row.createCell(4)
                .setCellValue(cliente.getFechaNacimiento() != null ? cliente.getFechaNacimiento().toString() : "");
        row.createCell(5).setCellValue(cliente.getGenero() != null ? cliente.getGenero() : "");
        row.createCell(6).setCellValue(cliente.getCorreoElectronico() != null ? cliente.getCorreoElectronico() : "");
        row.createCell(7).setCellValue(cliente.getCuentaBancaria() != null ? cliente.getCuentaBancaria() : "");
        row.createCell(8).setCellValue(cliente.getDireccion() != null ? cliente.getDireccion() : "");
        row.createCell(9).setCellValue(cliente.getTipoTecnologia() != null ? cliente.getTipoTecnologia() : "");
        row.createCell(10).setCellValue(cliente.getVelocidad() != null ? cliente.getVelocidad() : "");
        row.createCell(11).setCellValue("");
        row.createCell(12).setCellValue(cliente.getPlanActual() != null ? cliente.getPlanActual() : "");
        row.createCell(13).setCellValue("");
        row.createCell(14).setCellValue("");
        row.createCell(15).setCellValue(cliente.getMovilContacto() != null ? cliente.getMovilContacto() : "");
        row.createCell(16).setCellValue(cliente.getFijoCompania() != null ? cliente.getFijoCompania() : "");
        row.createCell(17).setCellValue(
                cliente.getMovilesAPortar() != null ? String.join(", ", cliente.getMovilesAPortar()) : "");
        row.createCell(18).setCellValue("");
        row.createCell(19).setCellValue("");
        row.createCell(20).setCellValue("");
        row.createCell(21).setCellValue("");
        row.createCell(22)
                .setCellValue(
                        cliente.getUsuario() != null
                                ? (cliente.getUsuario().getNombre() != null ? cliente.getUsuario().getNombre() : "")
                                + " " +
                                (cliente.getUsuario().getApellido() != null ? cliente.getUsuario().getApellido()
                                        : "")
                                : "");
        row.createCell(23).setCellValue("");
        row.createCell(24).setCellValue(cliente.getObservacion() != null ? cliente.getObservacion() : "");
        row.createCell(25).setCellValue("");
        row.createCell(26).setCellValue(Boolean.TRUE.equals(cliente.getVentaRealizada()) ? "Sí" : "No");
        row.createCell(27).setCellValue(Boolean.TRUE.equals(cliente.getAutorizaEnergias()) ? "Sí" : "No");
        row.createCell(28).setCellValue(Boolean.TRUE.equals(cliente.getAutorizaSeguros()) ? "Sí" : "No");
        row.createCell(29).setCellValue(cliente.getNumeroMoviles() != null ? cliente.getNumeroMoviles() : "");
        row.createCell(30).setCellValue(Boolean.TRUE.equals(cliente.getDeseaPromocionesLowi()) ? "Sí" : "No");
    }

    /**
     * =========================================================================
     * 2) MÉTODO PARA EXPORTAR "INDIVIDUAL"
     * Un solo cliente en una hoja, con estructura vertical y secciones.
     * Versión optimizada para mejor rendimiento y con formato exacto como en la
     * imagen
     * =========================================================================
     */
    public byte[] generarExcelClienteIndividual(String movilContacto) {
        // Validar el parámetro de entrada
        if (movilContacto == null || movilContacto.trim().isEmpty()) {
            return new byte[0];
        }

        // Buscar el cliente por su número de móvil
        List<ClienteResidencial> clientes = clienteResidencialRepository.findByMovilContacto(movilContacto);
        if (clientes.isEmpty()) {
            return new byte[0]; // Si no se encontró ningún registro, retornar un array vacío.
        }

        // Usar el primer registro de la lista
        ClienteResidencial cliente = clientes.get(0);

        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Cliente Residencial");

            // Establecer ancho de columnas
            sheet.setColumnWidth(0, 6000); // Columna de etiquetas
            sheet.setColumnWidth(1, 8000); // Columna de valores

            // Crear estilos
            CellStyle headerStyle = crearEstiloEncabezado(workbook);
            CellStyle labelStyle = workbook.createCellStyle();
            CellStyle valueStyle = workbook.createCellStyle();

            // Configurar bordes para todas las celdas
            labelStyle.setBorderBottom(BorderStyle.THIN);
            labelStyle.setBorderTop(BorderStyle.THIN);
            labelStyle.setBorderLeft(BorderStyle.THIN);
            labelStyle.setBorderRight(BorderStyle.THIN);

            valueStyle.setBorderBottom(BorderStyle.THIN);
            valueStyle.setBorderTop(BorderStyle.THIN);
            valueStyle.setBorderLeft(BorderStyle.THIN);
            valueStyle.setBorderRight(BorderStyle.THIN);

            // Configurar fuentes
            Font labelFont = workbook.createFont();
            labelFont.setBold(true);
            labelStyle.setFont(labelFont);

            int rowNum = 0;

            // ==================== SECCIÓN 1: DATOS DEL CLIENTE ====================
            Row headerRow1 = sheet.createRow(rowNum++);
            Cell headerCell1 = headerRow1.createCell(0);
            headerCell1.setCellValue("DATOS DEL CLIENTE");
            headerCell1.setCellStyle(headerStyle);

            // Extender el encabezado a la segunda columna
            Cell headerExtend1 = headerRow1.createCell(1);
            headerExtend1.setCellStyle(headerStyle);

            // Fusionar las celdas del encabezado
            sheet.addMergedRegion(new CellRangeAddress(rowNum - 1, rowNum - 1, 0, 1));

            // Agregar ID
            agregarFilaConEstilo(sheet, rowNum++, "ID:", cliente.getId() != null ? cliente.getId().toString() : "",
                    labelStyle, valueStyle);

            // Agregar datos personales del cliente
            agregarFilaConEstilo(sheet, rowNum++, "NOMBRES Y APELLIDOS:", cliente.getNombresApellidos(), labelStyle,
                    valueStyle);
            agregarFilaConEstilo(sheet, rowNum++, "NIF/NIE:", cliente.getNifNie(), labelStyle, valueStyle);
            agregarFilaConEstilo(sheet, rowNum++, "NACIONALIDAD:", cliente.getNacionalidad(), labelStyle, valueStyle);
            agregarFilaConEstilo(sheet, rowNum++, "FECHA DE NACIMIENTO:",
                    cliente.getFechaNacimiento() != null ? cliente.getFechaNacimiento().toString() : "", labelStyle,
                    valueStyle);
            agregarFilaConEstilo(sheet, rowNum++, "FECHA DE CREACIÓN:",
                    cliente.getFechaCreacion() != null ? cliente.getFechaCreacion().toString() : "", labelStyle,
                    valueStyle);
            agregarFilaConEstilo(sheet, rowNum++, "CORREO ELECTRÓNICO:", cliente.getCorreoElectronico(), labelStyle,
                    valueStyle);
            agregarFilaConEstilo(sheet, rowNum++, "MÓVIL DE CONTACTO:", cliente.getMovilContacto(), labelStyle,
                    valueStyle);
            agregarFilaConEstilo(sheet, rowNum++, "FIJO COMPAÑÍA:", cliente.getFijoCompania(), labelStyle, valueStyle);
            agregarFilaConEstilo(sheet, rowNum++, "DIRECCIÓN:", cliente.getDireccion(), labelStyle, valueStyle);
            agregarFilaConEstilo(sheet, rowNum++, "CÓDIGO POSTAL:", cliente.getCodigoPostal(), labelStyle, valueStyle);
            agregarFilaConEstilo(sheet, rowNum++, "PROVINCIA:", cliente.getProvincia(), labelStyle, valueStyle);
            agregarFilaConEstilo(sheet, rowNum++, "DISTRITO:", cliente.getDistrito(), labelStyle, valueStyle);
            agregarFilaConEstilo(sheet, rowNum++, "CIUDAD:", cliente.getCiudad(), labelStyle, valueStyle);

            // Agregar información del agente/asesor
            agregarFilaConEstilo(sheet, rowNum++, "NÚMERO DE AGENTE:", cliente.getNumeroAgente(), labelStyle,
                    valueStyle);

            // Obtener información del asesor de forma segura
            String nombreAsesor = "No especificado";
            try {
                if (cliente.getUsuario() != null) {
                    // Intentar obtener el nombre y apellido del usuario
                    String nombre = "";
                    String apellido = "";

                    try {
                        nombre = cliente.getUsuario().getNombre();
                        if (nombre == null)
                            nombre = "";
                    } catch (Exception e) {
                        // Ignorar error y continuar con cadena vacía
                    }

                    try {
                        apellido = cliente.getUsuario().getApellido();
                        if (apellido == null)
                            apellido = "";
                    } catch (Exception e) {
                        // Ignorar error y continuar con cadena vacía
                    }

                    if (!nombre.isEmpty() || !apellido.isEmpty()) {
                        nombreAsesor = nombre + " " + apellido;
                    }
                }
            } catch (Exception e) {
                // En caso de cualquier error, usar el valor por defecto
            }
            agregarFilaConEstilo(sheet, rowNum++, "ASESOR:", nombreAsesor, labelStyle, valueStyle);

            // Obtener información del coordinador de forma segura
            String coordinador = "No asignado";
            try {
                if (cliente.getUsuario() != null && cliente.getUsuario().getCoordinador() != null) {
                    // Intentar obtener el nombre y apellido del coordinador
                    String nombre = "";
                    String apellido = "";

                    try {
                        nombre = cliente.getUsuario().getCoordinador().getNombre();
                        if (nombre == null)
                            nombre = "";
                    } catch (Exception e) {
                        // Ignorar error y continuar con cadena vacía
                    }

                    try {
                        apellido = cliente.getUsuario().getCoordinador().getApellido();
                        if (apellido == null)
                            apellido = "";
                    } catch (Exception e) {
                        // Ignorar error y continuar con cadena vacía
                    }

                    if (!nombre.isEmpty() || !apellido.isEmpty()) {
                        coordinador = nombre + " " + apellido;
                    }
                }
            } catch (Exception e) {
                // En caso de cualquier error, usar el valor por defecto
            }
            agregarFilaConEstilo(sheet, rowNum++, "COORDINADOR:", coordinador, labelStyle, valueStyle);

            rowNum++; // Espacio antes de la siguiente sección

            // ==================== SECCIÓN 2: DATOS DE LA PROMOCIÓN ====================
            Row headerRow2 = sheet.createRow(rowNum++);
            Cell headerCell2 = headerRow2.createCell(0);
            headerCell2.setCellValue("DATOS DE LA PROMOCIÓN");
            headerCell2.setCellStyle(headerStyle);

            // Extender el encabezado a la segunda columna
            Cell headerExtend2 = headerRow2.createCell(1);
            headerExtend2.setCellStyle(headerStyle);

            // Fusionar las celdas del encabezado
            sheet.addMergedRegion(new CellRangeAddress(rowNum - 1, rowNum - 1, 0, 1));

            // Agregar datos de promoción
            agregarFilaConEstilo(sheet, rowNum++, "CAMPAÑA:", cliente.getCampania(), labelStyle, valueStyle);
            agregarFilaConEstilo(sheet, rowNum++, "NÚMEROS MÓVILES:", cliente.getNumeroMoviles(), labelStyle,
                    valueStyle);
            agregarFilaConEstilo(sheet, rowNum++, "PLAN ACTUAL:", cliente.getPlanActual(), labelStyle, valueStyle);
            agregarFilaConEstilo(sheet, rowNum++, "TIPO DE PLAN:", cliente.getTipoPlan(), labelStyle, valueStyle);
            agregarFilaConEstilo(sheet, rowNum++, "TECNOLOGÍA:", cliente.getTipoTecnologia(), labelStyle, valueStyle);
            agregarFilaConEstilo(sheet, rowNum++, "TIPO DE FIBRA:", cliente.getTipoFibra(), labelStyle, valueStyle);
            agregarFilaConEstilo(sheet, rowNum++, "TIPO DE VELOCIDAD:", cliente.getVelocidad(), labelStyle, valueStyle);
            agregarFilaConEstilo(sheet, rowNum++, "TITULAR DEL SERVICIO:", cliente.getTitularDelServicio(), labelStyle,
                    valueStyle);

            // Agregar campo de fútbol con formato especial para "No especificado"
            String futbolValue = cliente.getFutbol();
            if (futbolValue == null || futbolValue.trim().isEmpty()) {
                futbolValue = "— No especificado";
            }
            agregarFilaConEstilo(sheet, rowNum++, "FÚTBOL:", futbolValue, labelStyle, valueStyle);

            // Agregar ICC
            agregarFilaConEstilo(sheet, rowNum++, "ICC:", cliente.getIcc(), labelStyle, valueStyle);

            // Agregar cuenta bancaria
            agregarFilaConEstilo(sheet, rowNum++, "CUENTA BANCARIA:", cliente.getCuentaBancaria(), labelStyle,
                    valueStyle);

            // Agregar permanencia
            agregarFilaConEstilo(sheet, rowNum++, "PERMANENCIA:", cliente.getPermanencia(), labelStyle, valueStyle);

            // Agregar móviles a portar
            if (cliente.getMovilesAPortar() != null && !cliente.getMovilesAPortar().isEmpty()) {
                agregarFilaConEstilo(sheet, rowNum++, "MÓVILES A PORTAR:",
                        String.join(", ", cliente.getMovilesAPortar()), labelStyle, valueStyle);
            } else {
                agregarFilaConEstilo(sheet, rowNum++, "MÓVILES A PORTAR:", "", labelStyle, valueStyle);
            }

            // Agregar observación
            agregarFilaConEstilo(sheet, rowNum++, "OBSERVACIÓN:", cliente.getObservacion(), labelStyle, valueStyle);

            rowNum++; // Espacio antes de la siguiente sección

            // ==================== SECCIÓN 3: AUTORIZACIONES Y CONFIRMACIONES
            // ====================
            Row headerRow3 = sheet.createRow(rowNum++);
            Cell headerCell3 = headerRow3.createCell(0);
            headerCell3.setCellValue("AUTORIZACIONES Y CONFIRMACIONES");
            headerCell3.setCellStyle(headerStyle);

            // Extender el encabezado a la segunda columna
            Cell headerExtend3 = headerRow3.createCell(1);
            headerExtend3.setCellStyle(headerStyle);

            // Fusionar las celdas del encabezado
            sheet.addMergedRegion(new CellRangeAddress(rowNum - 1, rowNum - 1, 0, 1));

            // Agregar autorizaciones con formato especial para "No"
            agregarFilaConEstiloYSimbolo(sheet, rowNum++, "DESEA PROMOCIONES LOWI O COSTOS MAS BAJOS:",
                    Boolean.TRUE.equals(cliente.getDeseaPromocionesLowi()), labelStyle, valueStyle);
            agregarFilaConEstiloYSimbolo(sheet, rowNum++, "AUTORIZA SEGUROS:",
                    Boolean.TRUE.equals(cliente.getAutorizaSeguros()), labelStyle, valueStyle);
            agregarFilaConEstiloYSimbolo(sheet, rowNum++, "AUTORIZA ENERGÍAS:",
                    Boolean.TRUE.equals(cliente.getAutorizaEnergias()), labelStyle, valueStyle);
            agregarFilaConEstiloYSimbolo(sheet, rowNum++, "VENTA REALIZADA:",
                    Boolean.TRUE.equals(cliente.getVentaRealizada()), labelStyle, valueStyle);

            // Escribir directamente al stream de salida con buffer preasignado para mayor
            // velocidad
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream(1024 * 1024); // Preasignar 1MB
            workbook.write(outputStream);

            return outputStream.toByteArray();

        } catch (IOException e) {
            e.printStackTrace();
            return new byte[0];
        } catch (Exception e) {
            // Capturar cualquier otra excepción para evitar que el servidor se bloquee
            e.printStackTrace();
            return new byte[0];
        }
    }

    // Método auxiliar para agregar filas con estilos en el Excel "vertical"
    private void agregarFilaConEstilo(Sheet sheet, int rowNum, String titulo, String valor, CellStyle labelStyle,
                                      CellStyle valueStyle) {
        if (valor == null)
            valor = "";
        Row row = sheet.createRow(rowNum);

        Cell labelCell = row.createCell(0);
        labelCell.setCellValue(titulo);
        labelCell.setCellStyle(labelStyle);

        Cell valueCell = row.createCell(1);
        valueCell.setCellValue(valor);
        valueCell.setCellStyle(valueStyle);
    }

    // Método auxiliar para agregar filas con símbolos de Sí/No
    private void agregarFilaConEstiloYSimbolo(Sheet sheet, int rowNum, String titulo, boolean valor,
                                              CellStyle labelStyle, CellStyle valueStyle) {
        Row row = sheet.createRow(rowNum);

        Cell labelCell = row.createCell(0);
        labelCell.setCellValue(titulo);
        labelCell.setCellStyle(labelStyle);

        Cell valueCell = row.createCell(1);

        // Usar símbolos Unicode para representar Sí/No como en la imagen
        if (valor) {
            valueCell.setCellValue("Sí");
        } else {
            // Usar el símbolo X en rojo como en la imagen
            valueCell.setCellValue("✗ No");

            // Crear un estilo con texto rojo para el "No"
            CellStyle redTextStyle = sheet.getWorkbook().createCellStyle();
            redTextStyle.cloneStyleFrom(valueStyle);
            Font redFont = sheet.getWorkbook().createFont();
            redFont.setColor(IndexedColors.RED.getIndex());
            redTextStyle.setFont(redFont);

            valueCell.setCellStyle(redTextStyle);
            return;
        }

        valueCell.setCellStyle(valueStyle);
    }

    /**
     * Método para exportar a Excel los clientes filtrados por fecha de creación.
     * Versión ultra-rápida con optimizaciones extremas para rendimiento.
     * Optimizado para servidor con 188 GB de RAM.
     */
    public byte[] generarExcelClientesPorFecha(LocalDate fecha) {
        // Definir el rango del día (00:00 hasta 23:59:59)
        LocalDateTime inicioDelDia = fecha.atStartOfDay();
        LocalDateTime finDelDia = fecha.atTime(LocalTime.MAX);

        return generarExcelClientesPorRangoFechas(inicioDelDia, finDelDia);
    }

    /**
     * Método para exportar a Excel los clientes filtrados por un rango de fechas de
     * creación.
     * Versión optimizada para generar todo en una sola hoja.
     *
     * @param fechaInicio Fecha y hora de inicio del rango
     * @param fechaFin    Fecha y hora de fin del rango
     * @return Array de bytes con el archivo Excel generado
     */
    public byte[] generarExcelClientesPorRangoFechas(LocalDateTime fechaInicio, LocalDateTime fechaFin) {
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            // Crear formato base para nombre de hoja
            String fechaInicioStr = fechaInicio.toLocalDate().format(DateTimeFormatter.ISO_LOCAL_DATE);
            String fechaFinStr = fechaFin.toLocalDate().format(DateTimeFormatter.ISO_LOCAL_DATE);
            String nombreHoja = "Clientes_" + fechaInicioStr + "_a_" + fechaFinStr;

            // Si el nombre es muy largo, acortarlo a 31 caracteres (límite de Excel)
            if (nombreHoja.length() > 31) {
                nombreHoja = nombreHoja.substring(0, 31);
            }

            // Crear una sola hoja
            Sheet sheet = workbook.createSheet(nombreHoja);

            // Definir headers
            String[] headers = {
                    "ID", "CAMPAÑA", "NOMBRES", "NIF/NIE", "NACIONALIDAD", "NACIMIENTO",
                    "GÉNERO", "EMAIL", "CUENTA", "PERMANENCIA", "DIRECCIÓN", "TIPO FIBRA", "TECNOLOGIA",
                    "VELOCIDAD", "MOVIL", "FIJO", "PLAN ACTUAL", "COMERCIAL", "OBSERVACIONES", "NÚMERO MÓVILES",
                    "ICC", "NÚMERO AGENTE", "TITULAR DEL SERVICIO", "FÚTBOL",
                    "DESEA PROMOCIONES LOWI", "AUTORIZA SEGUROS", "AUTORIZA ENERGÍAS", "VENTA REALIZADA",
                    "FECHA CREACIÓN"
            };

            // Crear estilo para headers
            CellStyle headerStyle = crearEstiloEncabezado(workbook);

            // Crear encabezados
            Row headerRow = sheet.createRow(0);
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // Obtener todos los datos de una vez (usando un límite muy alto)
            List<Object[]> datosClientes = clienteResidencialRepository.findClientesBasicosByRangoFechasWithOffset(
                    fechaInicio, fechaFin, 1000000, 0);

            // Si no hay datos, retornar array vacío
            if (datosClientes.isEmpty()) {
                return new byte[0];
            }

            // Procesar todos los datos en una sola hoja
            int rowIndex = 1;
            for (Object[] datos : datosClientes) {
                Row dataRow = sheet.createRow(rowIndex++);

                // Llenar todas las columnas
                for (int i = 0; i < datos.length && i < headers.length; i++) {
                    Cell cell = dataRow.createCell(i);

                    if (datos[i] != null) {
                        // Convertir booleanos a "SI" o "NO"
                        if (datos[i] instanceof Boolean) {
                            boolean valor = (Boolean) datos[i];
                            cell.setCellValue(valor ? "SI" : "NO");
                        } else if ("true".equalsIgnoreCase(datos[i].toString())
                                || "false".equalsIgnoreCase(datos[i].toString())) {
                            // Si es un string "true" o "false", convertirlo a "SI" o "NO"
                            boolean valor = Boolean.parseBoolean(datos[i].toString());
                            cell.setCellValue(valor ? "SI" : "NO");
                        } else {
                            cell.setCellValue(datos[i].toString());
                        }
                    } else {
                        cell.setCellValue("");
                    }
                }
            }

            // Ajustar anchos de columna para que se vean completas las cabeceras
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // Escribir al stream de salida
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            return outputStream.toByteArray();

        } catch (Exception e) {
            e.printStackTrace();
            return new byte[0];
        }
    }

    /**
     * =========================================================================
     * MÉTODOS AUXILIARES
     * =========================================================================
     */

    // Crea un estilo de encabezado (fondo verde y texto en blanco, negrita)
    private CellStyle crearEstiloEncabezado(Workbook workbook) {
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setColor(IndexedColors.WHITE.getIndex());
        headerStyle.setFont(headerFont);
        headerStyle.setFillForegroundColor(IndexedColors.GREEN.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        return headerStyle;
    }

    // Crea una celda con el valor 'texto' y aplica un estilo
    private void crearCeldaConEstilo(Row row, int columnIndex, String texto, CellStyle style) {
        Cell cell = row.createCell(columnIndex);
        cell.setCellValue(texto);
        cell.setCellStyle(style);
    }

    /**
     * Exporta a Excel los clientes asignados hoy a los asesores de un coordinador
     * Versión optimizada para evitar sobrecargar el servidor
     */
    public byte[] exportarClientesDeAsesoresAsignadosHoy(Long coordinadorId) {
        LocalDate hoy = LocalDate.now();

        // Obtener los asesores del coordinador
        List<User> asesores = userRepository.findByCoordinadorId(coordinadorId);
        if (asesores.isEmpty())
            return new byte[0];

        List<Long> idsAsesores = asesores.stream().map(User::getId).toList();

        // Constantes para controlar el rendimiento
        final int MAX_CLIENTES = 10000; // Límite máximo de clientes a procesar

        // Obtener los clientes con paginación para evitar cargar todos a la vez
        List<ClienteConUsuarioDTO> clientesDTO = clienteResidencialRepository
                .findClientesConUsuarioPorFechaYAsesores(hoy, idsAsesores);

        // Limitar la cantidad de clientes a procesar
        int totalClientes = Math.min(clientesDTO.size(), MAX_CLIENTES);
        List<ClienteConUsuarioDTO> clientesDTOLimitados = clientesDTO.subList(0, totalClientes);

        // Extraer los números de móvil
        List<String> moviles = clientesDTOLimitados.stream()
                .map(ClienteConUsuarioDTO::getNumeroMovil)
                .toList();

        // Si no hay móviles, retornar un array vacío
        if (moviles.isEmpty())
            return new byte[0];

        // Cargar los clientes completos
        List<ClienteResidencial> clientes = clienteResidencialRepository.findByMovilContactoIn(moviles);

        return generarExcelDesdeClientes(clientes);
    }

    /**
     * Método ULTRA-RÁPIDO para exportar clientes por sede y fecha usando consulta
     * directa
     * Versión mejorada con consistencia total en ordenamiento y campos
     *
     * @param sedeId       ID de la sede (opcional)
     * @param supervisorId ID del supervisor (opcional)
     * @param fecha        Fecha específica
     * @return Array de bytes con el archivo Excel generado
     */
    public byte[] generarExcelEstadisticasOptimizado(Long sedeId, Long supervisorId, LocalDate fecha) {
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            // Crear formato base para nombre de hoja
            String nombreHoja = "Estadisticas_" + fecha.format(DateTimeFormatter.ISO_LOCAL_DATE);
            if (sedeId != null) {
                nombreHoja += "_Sede_" + sedeId;
            }
            if (supervisorId != null) {
                nombreHoja += "_Supervisor_" + supervisorId;
            }

            // Truncar el nombre si es muy largo (Excel tiene límite de 31 caracteres)
            if (nombreHoja.length() > 31) {
                nombreHoja = nombreHoja.substring(0, 31);
            }

            XSSFSheet sheet = workbook.createSheet(nombreHoja);

            // Headers estandarizados - EXACTAMENTE los mismos campos que devuelven las
            // consultas
            String[] headers = {
                    "ID", "CAMPAÑA", "NOMBRES", "NIF/NIE", "NACIONALIDAD", "NACIMIENTO",
                    "GÉNERO", "EMAIL", "CUENTA", "DIRECCIÓN", "TIPO TECNOLOGÍA",
                    "VELOCIDAD", "MÓVIL", "FIJO", "COMERCIAL", "OBSERVACIONES", "NÚMERO MÓVILES",
                    "DESEA PROMOCIONES LOWI", "AUTORIZA SEGUROS", "AUTORIZA ENERGÍAS", "VENTA REALIZADA",
                    "FECHA CREACIÓN", "COORDINADOR"
            };

            // Crear estilo para headers
            CellStyle headerStyle = crearEstiloEncabezado(workbook);

            // Crear encabezados
            Row headerRow = sheet.createRow(0);
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // Obtener todos los datos de una vez usando consulta optimizada
            List<Object[]> datosClientes;

            if (sedeId == null && supervisorId == null) {
                // Sin filtros específicos, usar el método mejorado
                System.out.println("OPTIMIZADO: Sin filtros específicos, usando método mejorado");
                LocalDateTime fechaInicio = fecha.atStartOfDay();
                LocalDateTime fechaFin = fecha.atTime(LocalTime.MAX);
                datosClientes = clienteResidencialRepository.findClientesBasicosByRangoFechasWithOffsetMejorado(
                        fechaInicio, fechaFin, 1000000, 0);
            } else if (sedeId != null && supervisorId != null) {
                // Filtrar por sede y supervisor específicos
                System.out.println("OPTIMIZADO: Filtrando por sede Y supervisor");
                datosClientes = clienteResidencialRepository.findClientesBasicosPorSedeYSupervisorYFechaOptimizado(
                        sedeId, supervisorId, fecha, 1000000);
            } else if (sedeId != null) {
                // Filtrar solo por sede
                System.out.println("OPTIMIZADO: Filtrando solo por sede");
                datosClientes = clienteResidencialRepository.findClientesBasicosPorSedeYFechaOptimizado(
                        sedeId, fecha, 1000000);
            } else {
                // Solo supervisor - usar consulta por supervisor y fecha
                System.out.println("OPTIMIZADO: Filtrando solo por supervisor");
                LocalDateTime fechaInicio = fecha.atStartOfDay();
                LocalDateTime fechaFin = fecha.atTime(LocalTime.MAX);
                datosClientes = clienteResidencialRepository.findClientesBasicosPorSupervisorYRangoFechas(
                        supervisorId, fechaInicio, fechaFin, 1000000);
            }

            System.out.println("OPTIMIZADO: Total de registros encontrados: " + datosClientes.size());

            // Si no hay datos, retornar array vacío
            if (datosClientes.isEmpty()) {
                return new byte[0];
            }

            // Procesar todos los datos en una sola hoja con formato consistente
            int rowIndex = 1;
            for (Object[] datos : datosClientes) {
                Row dataRow = sheet.createRow(rowIndex++);

                // Llenar las celdas con los datos usando el mismo formato que el método base
                for (int i = 0; i < datos.length && i < headers.length; i++) {
                    Cell cell = dataRow.createCell(i);
                    Object valor = datos[i];

                    if (valor != null) {
                        // Convertir booleanos a "SI" o "NO" (consistente con método base)
                        if (valor instanceof Boolean) {
                            boolean valorBool = (Boolean) valor;
                            cell.setCellValue(valorBool ? "SI" : "NO");
                        } else if ("true".equalsIgnoreCase(valor.toString())
                                || "false".equalsIgnoreCase(valor.toString())) {
                            // Si es un string "true" o "false", convertirlo a "SI" o "NO"
                            boolean valorBool = Boolean.parseBoolean(valor.toString());
                            cell.setCellValue(valorBool ? "SI" : "NO");
                        } else if (valor instanceof LocalDateTime) {
                            cell.setCellValue(
                                    ((LocalDateTime) valor).format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss")));
                        } else if (valor instanceof LocalDate) {
                            cell.setCellValue(((LocalDate) valor).format(DateTimeFormatter.ofPattern("dd/MM/yyyy")));
                        } else {
                            cell.setCellValue(valor.toString());
                        }
                    } else {
                        cell.setCellValue("");
                    }
                }
            }

            // Auto-ajustar columnas
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // Convertir a bytes
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream.toByteArray();

        } catch (Exception e) {
            System.err.println("Error al generar Excel optimizado: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("Error al generar el archivo Excel optimizado", e);
        }
    }

    /**
     * Método ULTRA-RÁPIDO para exportar clientes por sede, supervisor y rango de
     * fechas
     * Versión mejorada con consistencia total en ordenamiento y campos
     *
     * @param sedeId       ID de la sede (opcional)
     * @param supervisorId ID del supervisor (opcional)
     * @param fechaInicio  Fecha de inicio del rango
     * @param fechaFin     Fecha de fin del rango
     * @return Array de bytes con el archivo Excel generado
     */
    public byte[] generarExcelEstadisticasPorRangoFechas(Long sedeId, Long supervisorId, LocalDate fechaInicio,
                                                         LocalDate fechaFin) {
        try {
            System.out.println("=== GENERANDO EXCEL POR RANGO DE FECHAS CON FILTROS (MEJORADO) ===");
            System.out.println("SedeId: " + sedeId);
            System.out.println("SupervisorId: " + supervisorId);
            System.out.println("Fecha Inicio: " + fechaInicio);
            System.out.println("Fecha Fin: " + fechaFin);

            // Convertir LocalDate a LocalDateTime para usar el método existente
            LocalDateTime fechaInicioDateTime = fechaInicio.atStartOfDay();
            LocalDateTime fechaFinDateTime = fechaFin.atTime(LocalTime.MAX);

            try (XSSFWorkbook workbook = new XSSFWorkbook()) {
                // Crear formato base para nombre de hoja
                String nombreHoja = "Estadisticas_" + fechaInicio.format(DateTimeFormatter.ISO_LOCAL_DATE)
                        + "_a_" + fechaFin.format(DateTimeFormatter.ISO_LOCAL_DATE);
                if (sedeId != null) {
                    nombreHoja += "_Sede_" + sedeId;
                }
                if (supervisorId != null) {
                    nombreHoja += "_Sup_" + supervisorId;
                }

                // Truncar el nombre si es muy largo (Excel tiene límite de 31 caracteres)
                if (nombreHoja.length() > 31) {
                    nombreHoja = nombreHoja.substring(0, 31);
                }

                XSSFSheet sheet = workbook.createSheet(nombreHoja);

                // Headers estandarizados - EXACTAMENTE los mismos campos que devuelven las
                // consultas
                String[] headers = {
                        "ID", "CAMPAÑA", "NOMBRES", "NIF/NIE", "NACIONALIDAD", "NACIMIENTO",
                        "GÉNERO", "EMAIL", "CUENTA", "DIRECCIÓN", "TIPO TECNOLOGÍA",
                        "VELOCIDAD", "MÓVIL", "FIJO", "COMERCIAL", "OBSERVACIONES", "NÚMERO MÓVILES",
                        "DESEA PROMOCIONES LOWI", "AUTORIZA SEGUROS", "AUTORIZA ENERGÍAS", "VENTA REALIZADA",
                        "FECHA CREACIÓN", "COORDINADOR"
                };

                // Crear estilo para headers
                CellStyle headerStyle = crearEstiloEncabezado(workbook);

                // Crear encabezados
                Row headerRow = sheet.createRow(0);
                for (int i = 0; i < headers.length; i++) {
                    Cell cell = headerRow.createCell(i);
                    cell.setCellValue(headers[i]);
                    cell.setCellStyle(headerStyle);
                }

                // Obtener todos los datos de una vez usando consulta optimizada con filtros
                List<Object[]> datosClientes;

                if (sedeId == null && supervisorId == null) {
                    // Sin filtros - usar método base pero con consulta mejorada
                    System.out.println("OPTIMIZADO: Sin filtros - usando consulta base mejorada");
                    datosClientes = clienteResidencialRepository.findClientesBasicosByRangoFechasWithOffsetMejorado(
                            fechaInicioDateTime, fechaFinDateTime, 1000000, 0);
                } else if (sedeId != null && supervisorId != null) {
                    // Filtrar por sede y supervisor específicos
                    System.out.println("OPTIMIZADO: Filtrando por sede Y supervisor en rango de fechas");
                    datosClientes = clienteResidencialRepository.findClientesBasicosPorSedeYSupervisorYRangoFechas(
                            sedeId, supervisorId, fechaInicioDateTime, fechaFinDateTime, 1000000);
                } else if (sedeId != null) {
                    // Filtrar solo por sede
                    System.out.println("OPTIMIZADO: Filtrando solo por sede en rango de fechas");
                    datosClientes = clienteResidencialRepository.findClientesBasicosPorSedeYRangoFechas(
                            sedeId, fechaInicioDateTime, fechaFinDateTime, 1000000);
                } else {
                    // Solo supervisor
                    System.out.println("OPTIMIZADO: Filtrando solo por supervisor en rango de fechas");
                    datosClientes = clienteResidencialRepository.findClientesBasicosPorSupervisorYRangoFechas(
                            supervisorId, fechaInicioDateTime, fechaFinDateTime, 1000000);
                }

                System.out.println("OPTIMIZADO: Total de registros encontrados: " + datosClientes.size());

                // Si no hay datos, retornar array vacío
                if (datosClientes.isEmpty()) {
                    return new byte[0];
                }

                // Procesar todos los datos en una sola hoja con formato consistente
                int rowIndex = 1;
                for (Object[] datos : datosClientes) {
                    Row dataRow = sheet.createRow(rowIndex++);

                    // Llenar las celdas con los datos usando el mismo formato que el método base
                    for (int i = 0; i < datos.length && i < headers.length; i++) {
                        Cell cell = dataRow.createCell(i);
                        Object valor = datos[i];

                        if (valor != null) {
                            // Convertir booleanos a "SI" o "NO" (consistente con método base)
                            if (valor instanceof Boolean) {
                                boolean valorBool = (Boolean) valor;
                                cell.setCellValue(valorBool ? "SI" : "NO");
                            } else if ("true".equalsIgnoreCase(valor.toString())
                                    || "false".equalsIgnoreCase(valor.toString())) {
                                // Si es un string "true" o "false", convertirlo a "SI" o "NO"
                                boolean valorBool = Boolean.parseBoolean(valor.toString());
                                cell.setCellValue(valorBool ? "SI" : "NO");
                            } else if (valor instanceof LocalDateTime) {
                                cell.setCellValue(((LocalDateTime) valor)
                                        .format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss")));
                            } else if (valor instanceof LocalDate) {
                                cell.setCellValue(
                                        ((LocalDate) valor).format(DateTimeFormatter.ofPattern("dd/MM/yyyy")));
                            } else {
                                cell.setCellValue(valor.toString());
                            }
                        } else {
                            cell.setCellValue("");
                        }
                    }
                }

                // Auto-ajustar columnas
                for (int i = 0; i < headers.length; i++) {
                    sheet.autoSizeColumn(i);
                }

                // Convertir a bytes
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                workbook.write(outputStream);
                return outputStream.toByteArray();
            }

        } catch (Exception e) {
            System.err.println("Error al generar Excel por rango de fechas con filtros: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("Error al generar el archivo Excel por rango de fechas con filtros", e);
        }
    }

    /**
     * Genera un Excel a partir de una lista de clientes
     * Versión optimizada para procesar lotes grandes de datos
     */
    public byte[] generarExcelDesdeClientes(List<ClienteResidencial> clientes) {
        if (clientes == null || clientes.isEmpty())
            return new byte[0];

        // Constantes para controlar el rendimiento
        final int LIMITE_POR_HOJA = 1000; // Reducido para mejor rendimiento
        final int MAX_REGISTROS = 10000; // Límite máximo de registros a exportar

        try (Workbook workbook = new XSSFWorkbook()) {
            CellStyle headerStyle = crearEstiloEncabezado(workbook);

            // Contador para el total de registros procesados
            int numHoja = 1;
            Sheet sheet = workbook.createSheet("Clientes Residenciales Hoy " + numHoja);

            // Crear encabezados una sola vez
            Row headerRow = sheet.createRow(0);
            crearCeldaConEstilo(headerRow, 0, "CAMPAÑA", headerStyle);
            crearCeldaConEstilo(headerRow, 1, "NOMBRES Y APELLIDOS", headerStyle);
            crearCeldaConEstilo(headerRow, 2, "NIF/NIE", headerStyle);
            crearCeldaConEstilo(headerRow, 3, "NACIONALIDAD", headerStyle);
            crearCeldaConEstilo(headerRow, 4, "FECHA DE NACIMIENTO", headerStyle);
            crearCeldaConEstilo(headerRow, 5, "GÉNERO", headerStyle);
            crearCeldaConEstilo(headerRow, 6, "CORREO ELECTRÓNICO", headerStyle);
            crearCeldaConEstilo(headerRow, 7, "CUENTA BANCARIA", headerStyle);
            crearCeldaConEstilo(headerRow, 8, "DIRECCIÓN", headerStyle);
            crearCeldaConEstilo(headerRow, 9, "TIPO DE FIBRA", headerStyle);
            crearCeldaConEstilo(headerRow, 10, "HORA DE INSTALACIÓN", headerStyle);
            crearCeldaConEstilo(headerRow, 11, "PROMOCIÓN", headerStyle);
            crearCeldaConEstilo(headerRow, 12, "TV/DECO", headerStyle);
            crearCeldaConEstilo(headerRow, 13, "GRABACIÓN OCM", headerStyle);
            crearCeldaConEstilo(headerRow, 14, "MOVIL CONTACTO", headerStyle);
            crearCeldaConEstilo(headerRow, 15, "FIJO/COMPAÑÍA", headerStyle);
            crearCeldaConEstilo(headerRow, 16, "MOVILES A PORTAR", headerStyle);
            crearCeldaConEstilo(headerRow, 17, "PRECIO PROMOCIÓN/TIEMPO", headerStyle);
            crearCeldaConEstilo(headerRow, 18, "PRECIO REAL O DESPUÉS DE PROMOCIÓN", headerStyle);
            crearCeldaConEstilo(headerRow, 19, "SEGMENTO", headerStyle);
            crearCeldaConEstilo(headerRow, 20, "COMENTARIOS RELEVANTES", headerStyle);
            crearCeldaConEstilo(headerRow, 21, "COMERCIAL", headerStyle);
            crearCeldaConEstilo(headerRow, 22, "ASIGNADO A", headerStyle);
            crearCeldaConEstilo(headerRow, 23, "OBSERVACIONES", headerStyle);
            crearCeldaConEstilo(headerRow, 24, "TIPO DE USUARIO", headerStyle);
            crearCeldaConEstilo(headerRow, 25, "AUT.VENTA", headerStyle);
            crearCeldaConEstilo(headerRow, 26, "AUT.ENERGIA", headerStyle);
            crearCeldaConEstilo(headerRow, 27, "AUT.SEGURO", headerStyle);
            crearCeldaConEstilo(headerRow, 28, "NÚMERO MÓVILES", headerStyle);

            // Inicializar contador de filas para la hoja actual
            int rowIndex = 1;

            // Procesar los clientes en lotes para reducir el uso de memoria
            int totalClientes = Math.min(clientes.size(), MAX_REGISTROS);
            for (int i = 0; i < totalClientes; i++) {
                // Si alcanzamos el límite por hoja, crear una nueva hoja
                if (rowIndex > LIMITE_POR_HOJA) {
                    // Ajustar ancho de columnas antes de cambiar de hoja
                    for (int col = 0; col <= 28; col++) {
                        sheet.autoSizeColumn(col);
                    }

                    // Crear nueva hoja
                    numHoja++;
                    sheet = workbook.createSheet("Clientes Residenciales Hoy " + numHoja);
                    headerRow = sheet.createRow(0);

                    // Recrear encabezados en la nueva hoja
                    crearCeldaConEstilo(headerRow, 0, "CAMPAÑA", headerStyle);
                    crearCeldaConEstilo(headerRow, 1, "NOMBRES Y APELLIDOS", headerStyle);
                    crearCeldaConEstilo(headerRow, 2, "NIF/NIE", headerStyle);
                    crearCeldaConEstilo(headerRow, 3, "NACIONALIDAD", headerStyle);
                    crearCeldaConEstilo(headerRow, 4, "FECHA DE NACIMIENTO", headerStyle);
                    crearCeldaConEstilo(headerRow, 5, "GÉNERO", headerStyle);
                    crearCeldaConEstilo(headerRow, 6, "CORREO ELECTRÓNICO", headerStyle);
                    crearCeldaConEstilo(headerRow, 7, "CUENTA BANCARIA", headerStyle);
                    crearCeldaConEstilo(headerRow, 8, "DIRECCIÓN", headerStyle);
                    crearCeldaConEstilo(headerRow, 9, "TIPO DE FIBRA", headerStyle);
                    crearCeldaConEstilo(headerRow, 10, "HORA DE INSTALACIÓN", headerStyle);
                    crearCeldaConEstilo(headerRow, 11, "PROMOCIÓN", headerStyle);
                    crearCeldaConEstilo(headerRow, 12, "TV/DECO", headerStyle);
                    crearCeldaConEstilo(headerRow, 13, "GRABACIÓN OCM", headerStyle);
                    crearCeldaConEstilo(headerRow, 14, "MOVIL CONTACTO", headerStyle);
                    crearCeldaConEstilo(headerRow, 15, "FIJO/COMPAÑÍA", headerStyle);
                    crearCeldaConEstilo(headerRow, 16, "MOVILES A PORTAR", headerStyle);
                    crearCeldaConEstilo(headerRow, 17, "PRECIO PROMOCIÓN/TIEMPO", headerStyle);
                    crearCeldaConEstilo(headerRow, 18, "PRECIO REAL O DESPUÉS DE PROMOCIÓN", headerStyle);
                    crearCeldaConEstilo(headerRow, 19, "SEGMENTO", headerStyle);
                    crearCeldaConEstilo(headerRow, 20, "COMENTARIOS RELEVANTES", headerStyle);
                    crearCeldaConEstilo(headerRow, 21, "COMERCIAL", headerStyle);
                    crearCeldaConEstilo(headerRow, 22, "ASIGNADO A", headerStyle);
                    crearCeldaConEstilo(headerRow, 23, "OBSERVACIONES", headerStyle);
                    crearCeldaConEstilo(headerRow, 24, "TIPO DE USUARIO", headerStyle);
                    crearCeldaConEstilo(headerRow, 25, "AUT.VENTA", headerStyle);
                    crearCeldaConEstilo(headerRow, 26, "AUT.ENERGIA", headerStyle);
                    crearCeldaConEstilo(headerRow, 27, "AUT.SEGURO", headerStyle);
                    crearCeldaConEstilo(headerRow, 28, "NÚMERO MÓVILES", headerStyle);

                    // Reiniciar el índice de fila para la nueva hoja
                    rowIndex = 1;
                }

                ClienteResidencial cliente = clientes.get(i);
                Row row = sheet.createRow(rowIndex++);

                // Usar el método auxiliar para llenar las celdas
                llenarCeldasCliente(row, cliente);

            }

            // Ajustar ancho de columnas para la última hoja
            for (int i = 0; i <= 28; i++) {
                sheet.autoSizeColumn(i);
            }

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream.toByteArray();

        } catch (IOException e) {
            e.printStackTrace();
            return new byte[0];
        }
    }

    /**
     * Método ULTRA-RÁPIDO para exportar clientes con TODOS los filtros aplicados
     * incluyendo búsqueda por vendedor
     *
     * @param sedeId           ID de la sede (opcional)
     * @param supervisorId     ID del supervisor (opcional)
     * @param fecha            Fecha específica (opcional)
     * @param fechaInicio      Fecha de inicio del rango (opcional)
     * @param fechaFin         Fecha de fin del rango (opcional)
     * @param busquedaVendedor Término de búsqueda para filtrar por vendedor
     *                         (opcional)
     * @return Array de bytes con el archivo Excel generado
     */
    public byte[] generarExcelEstadisticasFiltrado(Long sedeId, Long supervisorId, LocalDate fecha,
                                                   LocalDate fechaInicio, LocalDate fechaFin, String busquedaVendedor) {
        try {
            System.out.println("=== GENERANDO EXCEL FILTRADO ===");
            System.out.println("SedeId: " + sedeId);
            System.out.println("SupervisorId: " + supervisorId);
            System.out.println("Fecha: " + fecha);
            System.out.println("Fecha Inicio: " + fechaInicio);
            System.out.println("Fecha Fin: " + fechaFin);
            System.out.println("Búsqueda Vendedor: " + busquedaVendedor);

            // Crear workbook
            try (XSSFWorkbook workbook = new XSSFWorkbook()) {
                // Crear formato base para nombre de hoja
                String nombreHoja = "Leads_Filtrados";
                if (fecha != null) {
                    nombreHoja += "_" + fecha.format(DateTimeFormatter.ISO_LOCAL_DATE);
                } else if (fechaInicio != null && fechaFin != null) {
                    nombreHoja += "_" + fechaInicio.format(DateTimeFormatter.ISO_LOCAL_DATE)
                            + "_a_" + fechaFin.format(DateTimeFormatter.ISO_LOCAL_DATE);
                }

                // Crear hoja
                XSSFSheet sheet = workbook.createSheet(nombreHoja);

                // Crear estilo para headers
                CellStyle headerStyle = crearEstiloEncabezado(workbook);

                // Crear encabezados
                Row headerRow = sheet.createRow(0);
                String[] headers = {
                        "ID", "CAMPAÑA", "NOMBRES Y APELLIDOS", "NIF/NIE", "NACIONALIDAD", "FECHA DE NACIMIENTO",
                        "GÉNERO", "CORREO ELECTRÓNICO", "CUENTA BANCARIA", "DIRECCIÓN", "TIPO TECNOLOGÍA",
                        "VELOCIDAD", "MÓVIL CONTACTO", "FIJO/COMPAÑÍA", "COMERCIAL", "OBSERVACIONES", "NÚMERO MÓVILES",
                        "DESEA PROMOCIONES LOWI", "AUTORIZA SEGUROS", "AUTORIZA ENERGÍAS", "VENTA REALIZADA",
                        "FECHA CREACIÓN", "PERMANENCIA", "PLAN ACTUAL", "TIPO PLAN", "ICC",
                        "CÓDIGO POSTAL", "PROVINCIA", "CIUDAD", "NÚMERO AGENTE", "MÓVILES A PORTAR", "COORDINADOR"
                };

                for (int i = 0; i < headers.length; i++) {
                    Cell cell = headerRow.createCell(i);
                    cell.setCellValue(headers[i]);
                    cell.setCellStyle(headerStyle);
                }

                // Obtener datos filtrados
                List<Object[]> datosClientes = obtenerDatosFiltrados(sedeId, supervisorId, fecha,
                        fechaInicio, fechaFin, busquedaVendedor);

                System.out.println("FILTRADO: Total de registros encontrados: " + datosClientes.size());

                // Si no hay datos, crear Excel con solo headers
                if (datosClientes.isEmpty()) {
                    System.out.println("No se encontraron datos, generando Excel solo con headers");
                    // Ajustar ancho de columnas solo para headers
                    for (int i = 0; i < headers.length; i++) {
                        sheet.autoSizeColumn(i);
                    }

                    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                    workbook.write(outputStream);
                    return outputStream.toByteArray();
                }

                // Procesar todos los datos
                int rowIndex = 1;
                for (Object[] datos : datosClientes) {
                    Row dataRow = sheet.createRow(rowIndex++);
                    llenarCeldasDatos(dataRow, datos);
                }

                // Ajustar ancho de columnas
                for (int i = 0; i < headers.length; i++) {
                    sheet.autoSizeColumn(i);
                }

                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                workbook.write(outputStream);
                return outputStream.toByteArray();
            }

        } catch (Exception e) {
            System.err.println("Error al generar Excel filtrado: " + e.getMessage());
            e.printStackTrace();

            // En caso de error, generar un Excel básico con mensaje de error
            try (XSSFWorkbook errorWorkbook = new XSSFWorkbook()) {
                XSSFSheet errorSheet = errorWorkbook.createSheet("Error");
                Row errorRow = errorSheet.createRow(0);
                Cell errorCell = errorRow.createCell(0);
                errorCell.setCellValue("Error al generar el reporte: " + e.getMessage());

                ByteArrayOutputStream errorStream = new ByteArrayOutputStream();
                errorWorkbook.write(errorStream);
                return errorStream.toByteArray();
            } catch (Exception ex) {
                System.err.println("Error crítico al generar Excel de error: " + ex.getMessage());
                // Como último recurso, retornar un array mínimo válido
                return new byte[] {};
            }
        }
    }

    /**
     * Obtiene datos filtrados según los parámetros proporcionados
     */
    private List<Object[]> obtenerDatosFiltrados(Long sedeId, Long supervisorId, LocalDate fecha,
                                                 LocalDate fechaInicio, LocalDate fechaFin, String busquedaVendedor) {

        // Si hay búsqueda por vendedor, usar consultas con filtro de nombre
        if (busquedaVendedor != null && !busquedaVendedor.trim().isEmpty()) {
            return obtenerDatosConFiltroVendedor(sedeId, supervisorId, fecha, fechaInicio, fechaFin, busquedaVendedor);
        }

        // Si no hay filtro de vendedor, usar los métodos existentes optimizados
        if (fecha != null) {
            return obtenerDatosPorFecha(sedeId, supervisorId, fecha);
        } else if (fechaInicio != null && fechaFin != null) {
            return obtenerDatosPorRangoFechas(sedeId, supervisorId, fechaInicio, fechaFin);
        }

        return new ArrayList<>();
    }

    /**
     * Obtiene datos con filtro de vendedor (búsqueda por nombre)
     */
    private List<Object[]> obtenerDatosConFiltroVendedor(Long sedeId, Long supervisorId, LocalDate fecha,
                                                         LocalDate fechaInicio, LocalDate fechaFin, String busquedaVendedor) {

        System.out.println("=== OBTENIENDO DATOS CON FILTRO VENDEDOR ===");
        System.out.println("SedeId: " + sedeId);
        System.out.println("SupervisorId: " + supervisorId);
        System.out.println("Fecha: " + fecha);
        System.out.println("FechaInicio: " + fechaInicio);
        System.out.println("FechaFin: " + fechaFin);
        System.out.println("BusquedaVendedor: " + busquedaVendedor);

        List<Object[]> resultado;

        if (fecha != null) {
            // Búsqueda por fecha específica con filtro de vendedor
            System.out.println("Ejecutando consulta por FECHA ESPECÍFICA con filtro vendedor");
            resultado = clienteResidencialRepository.findClientesBasicosPorFiltrosConBusquedaVendedor(
                    sedeId, supervisorId, fecha, null, null, busquedaVendedor, 1000000);
        } else if (fechaInicio != null && fechaFin != null) {
            // Búsqueda por rango de fechas con filtro de vendedor
            LocalDateTime fechaInicioDateTime = fechaInicio.atStartOfDay();
            LocalDateTime fechaFinDateTime = fechaFin.atTime(LocalTime.MAX);
            System.out.println("Ejecutando consulta por RANGO DE FECHAS con filtro vendedor");
            System.out.println("FechaInicioDateTime: " + fechaInicioDateTime);
            System.out.println("FechaFinDateTime: " + fechaFinDateTime);
            resultado = clienteResidencialRepository.findClientesBasicosPorFiltrosConBusquedaVendedor(
                    sedeId, supervisorId, null, fechaInicioDateTime, fechaFinDateTime, busquedaVendedor, 1000000);
        } else {
            System.out.println("No hay fecha ni rango de fechas válidos");
            resultado = new ArrayList<>();
        }

        System.out.println("Resultado de consulta con filtro vendedor: " + resultado.size() + " registros");
        return resultado;
    }

    /**
     * Obtiene datos por fecha específica usando métodos existentes
     */
    private List<Object[]> obtenerDatosPorFecha(Long sedeId, Long supervisorId, LocalDate fecha) {
        if (sedeId == null && supervisorId == null) {
            LocalDateTime fechaInicio = fecha.atStartOfDay();
            LocalDateTime fechaFin = fecha.atTime(LocalTime.MAX);
            return clienteResidencialRepository.findClientesBasicosByRangoFechasWithOffsetMejorado(
                    fechaInicio, fechaFin, 1000000, 0);
        } else if (sedeId != null && supervisorId != null) {
            return clienteResidencialRepository.findClientesBasicosPorSedeYSupervisorYFechaOptimizado(
                    sedeId, supervisorId, fecha, 1000000);
        } else if (sedeId != null) {
            return clienteResidencialRepository.findClientesBasicosPorSedeYFechaOptimizado(
                    sedeId, fecha, 1000000);
        } else {
            LocalDateTime fechaInicio = fecha.atStartOfDay();
            LocalDateTime fechaFin = fecha.atTime(LocalTime.MAX);
            return clienteResidencialRepository.findClientesBasicosPorSupervisorYRangoFechas(
                    supervisorId, fechaInicio, fechaFin, 1000000);
        }
    }

    /**
     * Obtiene datos por rango de fechas usando métodos existentes
     */
    private List<Object[]> obtenerDatosPorRangoFechas(Long sedeId, Long supervisorId, LocalDate fechaInicio,
                                                      LocalDate fechaFin) {
        LocalDateTime fechaInicioDateTime = fechaInicio.atStartOfDay();
        LocalDateTime fechaFinDateTime = fechaFin.atTime(LocalTime.MAX);

        if (sedeId == null && supervisorId == null) {
            return clienteResidencialRepository.findClientesBasicosByRangoFechasWithOffsetMejorado(
                    fechaInicioDateTime, fechaFinDateTime, 1000000, 0);
        } else if (sedeId != null && supervisorId != null) {
            return clienteResidencialRepository.findClientesBasicosPorSedeYSupervisorYRangoFechas(
                    sedeId, supervisorId, fechaInicioDateTime, fechaFinDateTime, 1000000);
        } else if (sedeId != null) {
            return clienteResidencialRepository.findClientesBasicosPorSedeYRangoFechas(
                    sedeId, fechaInicioDateTime, fechaFinDateTime, 1000000);
        } else {
            return clienteResidencialRepository.findClientesBasicosPorSupervisorYRangoFechas(
                    supervisorId, fechaInicioDateTime, fechaFinDateTime, 1000000);
        }
    }

    /**
     * Método auxiliar para llenar las celdas de datos en una fila
     * Maneja la conversión de tipos y valores nulos de forma consistente
     */
    private void llenarCeldasDatos(Row dataRow, Object[] datos) {
        for (int i = 0; i < datos.length; i++) {
            Cell cell = dataRow.createCell(i);
            Object valor = datos[i];

            if (valor != null) {
                if (valor instanceof Boolean) {
                    cell.setCellValue(((Boolean) valor) ? "Sí" : "No");
                } else if (valor instanceof LocalDate) {
                    cell.setCellValue(((LocalDate) valor).format(DateTimeFormatter.ISO_LOCAL_DATE));
                } else if (valor instanceof LocalDateTime) {
                    cell.setCellValue(
                            ((LocalDateTime) valor).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                } else {
                    cell.setCellValue(valor.toString());
                }
            } else {
                cell.setCellValue("");
            }
        }
    }
}