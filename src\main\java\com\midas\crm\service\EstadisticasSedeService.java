package com.midas.crm.service;

import com.midas.crm.entity.DTO.EstadisticaSedeDTO;
import com.midas.crm.entity.DTO.EstadisticaSedePaginadaResponse;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * Servicio para manejar las estadísticas por sede
 */
public interface EstadisticasSedeService {

    /**
     * Obtiene estadísticas agrupadas por sede, supervisor y vendedor para una fecha
     * específica
     *
     * @param sedeId ID de la sede (opcional, null para todas las sedes)
     * @param fecha  Fecha para filtrar
     * @return Lista de estadísticas
     */
    List<EstadisticaSedeDTO> obtenerEstadisticasPorSede(Long sedeId, LocalDate fecha);

    /**
     * Obtiene estadísticas resumidas por sede para una fecha específica
     *
     * @param fecha Fecha para filtrar
     * @return Lista de estadísticas resumidas
     */
    List<EstadisticaSedeDTO> obtenerResumenPorSede(LocalDate fecha);

    /**
     * Obtiene estadísticas por rango de fechas
     *
     * @param fechaInicio Fecha de inicio
     * @param fechaFin    Fecha de fin
     * @param sedeId      ID de la sede (opcional)
     * @return Lista de estadísticas
     */
    List<EstadisticaSedeDTO> obtenerEstadisticasPorRango(LocalDate fechaInicio, LocalDate fechaFin, Long sedeId);

    // ===== MÉTODOS PAGINADOS =====

    /**
     * Obtiene estadísticas agrupadas por sede, supervisor y vendedor para una fecha
     * específica con paginación
     *
     * @param sedeId       ID de la sede (opcional, null para todas las sedes)
     * @param supervisorId ID del supervisor (opcional, null para todos los
     *                     supervisores)
     * @param fecha        Fecha para filtrar
     * @param pageable     Información de paginación
     * @return Respuesta paginada con estadísticas
     */
    EstadisticaSedePaginadaResponse obtenerEstadisticasPorSedePaginado(Long sedeId, Long supervisorId,
                                                                       LocalDate fecha,
                                                                       Pageable pageable);

    /**
     * Obtiene estadísticas agrupadas por sede, supervisor y vendedor para una fecha
     * específica con paginación y filtro de búsqueda por nombre de vendedor
     *
     * @param sedeId           ID de la sede (opcional, null para todas las sedes)
     * @param supervisorId     ID del supervisor (opcional, null para todos los
     *                         supervisores)
     * @param fecha            Fecha para filtrar
     * @param busquedaVendedor Término de búsqueda para el nombre del vendedor
     *                         (opcional)
     * @param pageable         Información de paginación
     * @return Respuesta paginada con estadísticas
     */
    EstadisticaSedePaginadaResponse obtenerEstadisticasPorSedePaginadoConBusqueda(Long sedeId, Long supervisorId,
                                                                                  LocalDate fecha, String busquedaVendedor,
                                                                                  Pageable pageable);

    /**
     * Obtiene estadísticas por rango de fechas con paginación
     *
     * @param fechaInicio Fecha de inicio
     * @param fechaFin    Fecha de fin
     * @param sedeId      ID de la sede (opcional)
     * @param pageable    Información de paginación
     * @return Respuesta paginada con estadísticas
     */
    EstadisticaSedePaginadaResponse obtenerEstadisticasPorRangoPaginado(LocalDate fechaInicio, LocalDate fechaFin,
                                                                        Long sedeId, Pageable pageable);

    /**
     * Obtiene leads específicos de un asesor para una fecha determinada
     *
     * @param nombreAsesor Nombre completo del asesor
     * @param fecha        Fecha para filtrar
     * @param numeroMovil  Número móvil para filtrar (opcional)
     * @param pageable     Información de paginación
     * @return Respuesta paginada con los leads del asesor
     */
    Map<String, Object> obtenerLeadsPorAsesorYFecha(String nombreAsesor, LocalDate fecha, String numeroMovil,
                                                    Pageable pageable);

    /**
     * Obtiene leads específicos de un asesor para un rango de fechas
     *
     * @param nombreAsesor Nombre completo del asesor
     * @param fechaInicio  Fecha de inicio para filtrar
     * @param fechaFin     Fecha de fin para filtrar
     * @param numeroMovil  Número móvil para filtrar (opcional)
     * @param pageable     Información de paginación
     * @return Respuesta paginada con los leads del asesor
     */
    Map<String, Object> obtenerLeadsPorAsesorYRangoFechas(String nombreAsesor, LocalDate fechaInicio,
                                                          LocalDate fechaFin, String numeroMovil, Pageable pageable);

    /**
     * Obtiene supervisores/coordinadores por sede
     *
     * @param sedeId ID de la sede
     * @return Lista de supervisores de la sede
     */
    List<Map<String, Object>> obtenerSupervisoresPorSede(Long sedeId);

    /**
     * Exporta a Excel todos los leads filtrados por sede, supervisor y fecha
     * específica
     *
     * @param sedeId       ID de la sede (opcional)
     * @param supervisorId ID del supervisor (opcional)
     * @param fecha        Fecha para filtrar
     * @return Array de bytes con el archivo Excel generado
     */
    byte[] exportarLeadsPorRango(Long sedeId, Long supervisorId, LocalDate fecha);

    /**
     * Obtiene estadísticas acumuladas por rango de fechas con paginación
     *
     * @param sedeId       ID de la sede (opcional)
     * @param supervisorId ID del supervisor (opcional)
     * @param fechaInicio  Fecha de inicio del rango
     * @param fechaFin     Fecha de fin del rango
     * @param pageable     Información de paginación
     * @return Estadísticas acumuladas paginadas
     */
    EstadisticaSedePaginadaResponse obtenerEstadisticasPorRangoFechas(Long sedeId, Long supervisorId,
                                                                      LocalDate fechaInicio, LocalDate fechaFin, Pageable pageable);

    /**
     * Obtiene estadísticas acumuladas por rango de fechas con paginación y búsqueda
     * por vendedor
     *
     * @param sedeId           ID de la sede (opcional)
     * @param supervisorId     ID del supervisor (opcional)
     * @param fechaInicio      Fecha de inicio del rango
     * @param fechaFin         Fecha de fin del rango
     * @param busquedaVendedor Término de búsqueda para el nombre del vendedor
     *                         (opcional)
     * @param pageable         Información de paginación
     * @return Estadísticas acumuladas paginadas
     */
    EstadisticaSedePaginadaResponse obtenerEstadisticasPorRangoFechasConBusqueda(Long sedeId, Long supervisorId,
                                                                                 LocalDate fechaInicio, LocalDate fechaFin, String busquedaVendedor, Pageable pageable);

    /**
     * Exporta a Excel todos los leads filtrados por sede, supervisor y rango de
     * fechas
     *
     * @param sedeId       ID de la sede (opcional)
     * @param supervisorId ID del supervisor (opcional)
     * @param fechaInicio  Fecha de inicio del rango
     * @param fechaFin     Fecha de fin del rango
     * @return Array de bytes con el archivo Excel generado
     */
    byte[] exportarLeadsPorRangoFechas(Long sedeId, Long supervisorId, LocalDate fechaInicio, LocalDate fechaFin);

    /**
     * Exporta a Excel todos los leads filtrados incluyendo búsqueda por vendedor
     * Este método respeta TODOS los filtros aplicados en el frontend
     *
     * @param sedeId           ID de la sede (opcional)
     * @param supervisorId     ID del supervisor (opcional)
     * @param fecha            Fecha específica (opcional, para exportar por fecha)
     * @param fechaInicio      Fecha de inicio del rango (opcional, para exportar
     *                         por rango)
     * @param fechaFin         Fecha de fin del rango (opcional, para exportar por
     *                         rango)
     * @param busquedaVendedor Término de búsqueda para filtrar por vendedor
     *                         (opcional)
     * @return Array de bytes con el archivo Excel generado
     */
    byte[] exportarLeadsFiltrados(Long sedeId, Long supervisorId, LocalDate fecha,
                                  LocalDate fechaInicio, LocalDate fechaFin, String busquedaVendedor);
}
