package com.midas.crm.service;

import com.midas.crm.entity.DTO.manual.ManualDTO;
import com.midas.crm.entity.Manual;
import com.midas.crm.entity.Role;
import org.springframework.data.domain.Page;

import org.springframework.web.multipart.MultipartFile;
import java.util.List;

public interface ManualService {
    /**
     * Obtiene una página de manuales con filtros opcionales
     */
    Page<Manual> index(String search, int page, int size, String column, String order);

    /**
     * Obtiene una página de manuales considerando el rol del usuario
     * Los usuarios ADMIN pueden ver todos los manuales (activos e inactivos)
     * Los demás usuarios solo pueden ver manuales activos
     */
    Page<Manual> indexByRole(String search, int page, int size, String column, String order, Role role);

    /**
     * Obtiene todos los manuales
     */
    List<Manual> getAll();

    /**
     * Obtiene todos los manuales según el rol del usuario
     */
    List<Manual> getAllByRole(Role role);

    /**
     * Obtiene un manual por su ID
     */
    Manual getById(int id);

    /**
     * Crea un nuevo manual
     */
    Manual create(ManualDTO dto, MultipartFile file);

    /**
     * Actualiza un manual existente
     */
    Manual update(int id, ManualDTO dto, MultipartFile file);

    /**
     * Primera etapa de eliminación: marca el manual como inactivo
     */
    boolean delete(int id, Long userDeleteId);

    /**
     * Segunda etapa de eliminación: elimina permanentemente un manual que ya está
     * inactivo
     */
    boolean permanentDelete(int id, Long userDeleteId);

    /**
     * Restaura un manual eliminado o inactivo
     */
    Manual restore(int id);
}
