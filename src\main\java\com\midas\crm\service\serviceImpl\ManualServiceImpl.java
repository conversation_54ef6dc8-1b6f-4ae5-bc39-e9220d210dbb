package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.DTO.manual.ManualDTO;
import com.midas.crm.mapper.ManualMapper;
import com.midas.crm.entity.Manual;
import com.midas.crm.entity.Role;
import com.midas.crm.repository.ManualRepository;
import com.midas.crm.service.ManualService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Service
public class ManualServiceImpl implements ManualService {

    @Autowired
    private ManualRepository manualRepository;

    @Override
    public Page<Manual> index(String search, int page, int size, String column, String order) {
        // Crear pageable usando programación funcional con Optional
        Pageable pageable = PageRequest.of(
                page,
                size,
                Sort.by(
                        Sort.Direction.fromString(Optional.ofNullable(order).orElse("DESC")),
                        Optional.ofNullable(column).orElse("id")));

        // Usar expresión lambda para determinar qué método del repositorio llamar
        Page<Manual> result = Optional.ofNullable(search)
                .filter(s -> !s.isEmpty())
                .map(s -> manualRepository.findByNombreContainingIgnoreCaseOrTipoContainingIgnoreCase(s, s, pageable))
                .orElseGet(() -> manualRepository.findAll(pageable));

        // Aplicar transformación a cada elemento usando método de referencia
        result.getContent().forEach(Manual::setTipoTextFromCode);
        return result;
    }

    @Override
    public Page<Manual> indexByRole(String search, int page, int size, String column, String order, Role role) {
        // Crear pageable usando programación funcional con Optional
        Pageable pageable = PageRequest.of(
                page,
                size,
                Sort.by(
                        Sort.Direction.fromString(Optional.ofNullable(order).orElse("DESC")),
                        Optional.ofNullable(column).orElse("id")));

        // Determinar si el usuario puede ver manuales inactivos
        boolean canSeeInactive = role == Role.ADMIN || role == Role.PROGRAMADOR;

        // Usar expresión lambda para determinar qué método del repositorio llamar
        Page<Manual> result;

        if (canSeeInactive) {
            // Administradores pueden ver todos los manuales (activos e inactivos)
            result = Optional.ofNullable(search)
                    .filter(s -> !s.isEmpty())
                    .map(s -> manualRepository.findByNombreContainingIgnoreCaseOrTipoContainingIgnoreCase(s, s,
                            pageable))
                    .orElseGet(() -> manualRepository.findAll(pageable));
        } else {
            // Otros usuarios solo pueden ver manuales activos
            result = Optional.ofNullable(search)
                    .filter(s -> !s.isEmpty())
                    .map(s -> manualRepository
                            .findByNombreContainingIgnoreCaseOrTipoContainingIgnoreCaseAndIsActiveTrue(s, s, pageable))
                    .orElseGet(() -> manualRepository.findAllByIsActiveTrue(pageable));
        }

        // Aplicar transformación a cada elemento usando método de referencia
        result.getContent().forEach(Manual::setTipoTextFromCode);
        return result;
    }

    @Override
    public List<Manual> getAll() {
        // Obtener manuales activos y aplicar transformación en un solo flujo
        return manualRepository.findAllActiveIncludingDeleted().stream()
                .peek(Manual::setTipoTextFromCode)
                .collect(Collectors.toList());
    }

    @Override
    public List<Manual> getAllByRole(Role role) {
        // Determinar si el usuario puede ver manuales inactivos
        boolean canSeeInactive = role == Role.ADMIN || role == Role.PROGRAMADOR;

        // Obtener manuales según el rol y aplicar transformación en un solo flujo
        if (canSeeInactive) {
            // Administradores pueden ver todos los manuales (activos e inactivos)
            return manualRepository.findAllIncludingDeleted().stream()
                    .peek(Manual::setTipoTextFromCode)
                    .collect(Collectors.toList());
        } else {
            // Otros usuarios solo pueden ver manuales activos
            return manualRepository.findAllActive().stream()
                    .peek(Manual::setTipoTextFromCode)
                    .collect(Collectors.toList());
        }
    }

    @Override
    public Manual getById(int id) {
        // Usar Optional para manejar el caso de manual no encontrado
        return manualRepository.findById(id)
                .map(manual -> {
                    manual.setTipoTextFromCode();
                    return manual;
                })
                .orElseThrow(() -> new RuntimeException("Manual no encontrado"));
    }

    @Override
    public Manual create(ManualDTO dto, MultipartFile file) {
        // Usar composición de funciones para transformar, guardar y procesar
        return Optional.of(dto)
                .map(ManualMapper::toEntity)
                .map(manualRepository::save)
                .map(saved -> {
                    saved.setTipoTextFromCode();
                    return saved;
                })
                .orElseThrow(() -> new RuntimeException("Error al crear el manual"));
    }

    /**
     * Actualiza un manual existente con los datos proporcionados en el DTO.
     * Permite actualizaciones parciales donde solo se modifican los campos que
     * vienen en el DTO.
     * Respeta el estado de eliminación (soft delete) del manual.
     * Utiliza una consulta nativa para evitar problemas con el bloqueo optimista.
     */
    @Override
    public Manual update(int id, ManualDTO dto, MultipartFile file) {
        try {
            // Usar Optional para manejar el flujo de verificación y actualización
            return manualRepository.findByIdIncludingDeleted(id)
                    .map(manual -> {
                        // Verificar si el manual está eliminado
                        if (manual.getDeletedAt() != null) {
                            throw new RuntimeException("No se puede actualizar un manual eliminado. ID: " + id);
                        }

                        // Preparar los datos para la actualización usando Optional
                        String nombre = Optional.ofNullable(dto.getNombre())
                                .filter(n -> !n.trim().isEmpty())
                                .map(String::trim)
                                .orElse(manual.getNombre());

                        String tipo = Optional.ofNullable(dto.getTipo())
                                .filter(t -> !t.trim().isEmpty())
                                .map(String::trim)
                                .orElse(manual.getTipo());

                        String archivo = Optional.ofNullable(dto.getArchivo())
                                .orElse(manual.getArchivo());

                        String horario = Optional.ofNullable(dto.getHorario())
                                .orElse(manual.getHorario());

                        Long userUpdateId = Optional.ofNullable(dto.getUserAuthId())
                                .orElse(manual.getUserUpdateId());

                        // Determinar el estado (isActive) del manual
                        Boolean isActive = manual.getIsActive(); // Valor actual por defecto

                        // Si viene en el DTO, usar ese valor (puede ser true o false)
                        if (dto.getIsActive() != null) {
                            isActive = dto.getIsActive();
                            System.out.println("Estado del manual recibido en DTO: " + isActive);
                        } else if (dto.getIs_active() != null) {
                            // Compatibilidad con el formato antiguo
                            isActive = dto.getIs_active();
                            System.out.println("Estado del manual recibido en formato antiguo: " + isActive);
                        }

                        // Si isActive es null (no debería ocurrir), establecer a true por defecto
                        if (isActive == null) {
                            isActive = true;
                        }

                        // Actualizar el manual usando una consulta nativa
                        int rowsUpdated = manualRepository.updateManualWithStateById(id, nombre, tipo, archivo, horario,
                                isActive, userUpdateId);

                        if (rowsUpdated == 0) {
                            throw new RuntimeException("No se pudo actualizar el manual con ID: " + id);
                        }

                        // Obtener y procesar el manual actualizado
                        return manualRepository.findById(id)
                                .map(updated -> {
                                    updated.setTipoTextFromCode();
                                    return updated;
                                })
                                .orElseThrow(() -> new RuntimeException(
                                        "No se pudo encontrar el manual actualizado con ID: " + id));
                    })
                    .orElseThrow(() -> new RuntimeException("Manual no encontrado con ID: " + id));
        } catch (Exception e) {
            // Registrar y relanzar la excepción para que sea manejada por el controlador
            System.err.println("Error al actualizar el manual: " + e.getMessage());
            throw new RuntimeException("Error al actualizar el manual: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean delete(int id, Long userDeleteId) {
        // Usar Optional para manejar el flujo de verificación y eliminación
        return manualRepository.findById(id)
                .map(manual -> {
                    // Primera etapa: marcar como inactivo con horario 00000
                    int rowsAffected = manualRepository.markAsInactiveById(id, userDeleteId);
                    return rowsAffected > 0;
                })
                .orElse(false);
    }

    @Override
    public boolean permanentDelete(int id, Long userDeleteId) {
        // Usar programación funcional para verificar y eliminar
        return Optional.of(id)
                .filter(manualRepository::isInactiveById)
                .map(manualId -> {
                    // Segunda etapa: eliminar permanentemente (soft delete con deleted_at)
                    int rowsAffected = manualRepository.permanentDeleteById(manualId, userDeleteId);
                    return rowsAffected > 0;
                })
                .orElse(false);
    }

    @Override
    public Manual restore(int id) {
        // Usar Optional y programación funcional para todo el flujo
        return manualRepository.findByIdIncludingDeleted(id)
                .map(manual -> {
                    // Verificar si el manual ya está activo
                    if (manual.getDeletedAt() == null &&
                            (manual.getHorario() == null || !manual.getHorario().equals("00000")) &&
                            manual.getIsActive()) {
                        return manual; // El manual ya está activo, no es necesario restaurarlo
                    }

                    // Restaurar el manual y obtener el resultado
                    return Optional.of(manualRepository.restoreById(id))
                            .filter(rowsAffected -> rowsAffected > 0)
                            .flatMap(rowsAffected -> manualRepository.findById(id))
                            .map(restoredManual -> {
                                restoredManual.setTipoTextFromCode();
                                return restoredManual;
                            })
                            .orElse(null);
                })
                .orElse(null);
    }
}
