package com.midas.crm.utils;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum MidasErrorMessage {

    // USUARIO
    USUARIO_ALREADY_EXISTS(101, "El usuario ya existe"),
    USUARIO_NOT_FOUND(102, "El usuario no existe"),
    USUARIO_INVALID_LOGIN(103, "Correo o contraseña inválidos"),
    USUARIO_INVALID_DATA(104, "Los datos del usuario son inválidos"),
    USUARIO_USERNAME_EXISTS(105, "El nombre de usuario ya existe"),
    USUARIO_DNI_EXISTS(106, "El DNI ya está registrado"),
    USUARIO_IMPORT_SUCCESS(107, "Usuarios importados exitosamente"),
    USUARIO_IMPORT_ERROR(108, "Error al importar usuarios desde Excel"),

    // CLIENTE RESIDENCIAL
    CLIENTERESIDENCIAL_ALREADY_EXISTS(201, "El cliente residencial ya existe"),
    CLIENTERESIDENCIAL_NOT_FOUND(202, "El cliente residencial no existe"),
    CLIENTERESIDENCIAL_INVALID_DATA(203, "Los datos del cliente residencial son inválidos"),

    // COORDINADOR
    COORDINADOR_ALREADY_EXISTS(301, "El coordinador ya existe"),
    COORDINADOR_NOT_FOUND(302, "El coordinador no existe"),
    COORDINADOR_NOT_AVAILABLE(303, "El coordinador no está disponible"),
    ASIGNAR_COORDINADOR_FAILED(304, "No se pudo asignar el coordinador"),

    // ASESOR
    ASESOR_NOT_FOUND(401, "El asesor no existe"),
    ASESOR_ALREADY_EXISTS(402, "El asesor ya existe"),
    ASESOR_NOT_AVAILABLE(403, "El asesor seleccionado no está disponible"),
    ASIGNACION_ASESOR_FAILED(404, "No se pudo realizar la asignación del asesor"),

    // SEDE
    SEDE_ALREADY_EXISTS(701, "La sede ya existe"),
    SEDE_NOT_FOUND(702, "La sede no existe"),
    SEDE_INVALID_DATA(703, "Los datos de la sede son inválidos"),

    // GENERAL
    ERROR_INTERNAL(500, "Ha ocurrido un error en el servidor"),
    TOKEN_INVALIDO(600, "Token inválido o corrupto"),

    // ANUNCIOS
    ANUNCIO_NOT_FOUND(601, "El anuncio no existe"),

    // CURSO
    CURSO_ALREADY_EXISTS(801, "El curso ya existe"),
    CURSO_NOT_FOUND(802, "El curso no existe"),
    CURSO_INVALID_DATA(803, "Los datos del curso son inválidos"),
    CURSO_USUARIO_NOT_FOUND(804, "El usuario asignado no existe"),
    CURSO_USUARIO_ALREADY_EXISTS(805, "El usuario ya está asignado a este curso"),

    // MÓDULO
    MODULO_ALREADY_EXISTS(811, "El módulo ya existe en este curso"),
    MODULO_NOT_FOUND(812, "El módulo no existe"),
    MODULO_INVALID_DATA(813, "Los datos del módulo son inválidos"),

    // SECCIÓN
    SECCION_ALREADY_EXISTS(816, "La sección ya existe en este módulo"),
    SECCION_NOT_FOUND(817, "La sección no existe"),
    SECCION_INVALID_DATA(818, "Los datos de la sección son inválidos"),

    // LECCIÓN
    LECCION_ALREADY_EXISTS(821, "La lección ya existe en esta sección"),
    LECCION_NOT_FOUND(822, "La lección no existe"),
    LECCION_INVALID_DATA(823, "Los datos de la lección son inválidos"),

    // PROGRESO USUARIO
    PROGRESO_NOT_FOUND(831, "No se encontró registro de progreso para este usuario y lección"),
    PROGRESO_INVALID_DATA(832, "Los datos del progreso son inválidos"),

    // VIDEO INFO
    VIDEO_INFO_ALREADY_EXISTS(841, "Ya existe información de video para esta lección"),
    VIDEO_INFO_NOT_FOUND(842, "No se encontró información de video para esta lección"),

    // CUESTIONARIO
    CUESTIONARIO_ALREADY_EXISTS(851, "Ya existe un cuestionario para esta lección"),
    CUESTIONARIO_NOT_FOUND(852, "El cuestionario no existe"),
    CUESTIONARIO_INVALID_DATA(853, "Los datos del cuestionario son inválidos"),
    CUESTIONARIO_MAX_INTENTOS(854, "Has alcanzado el número máximo de intentos para este cuestionario"),

    // PREGUNTA
    PREGUNTA_NOT_FOUND(861, "La pregunta no existe"),
    PREGUNTA_INVALID_DATA(862, "Los datos de la pregunta son inválidos"),

    // RESPUESTA
    RESPUESTA_NOT_FOUND(871, "La respuesta no existe"),
    RESPUESTA_INVALID_DATA(872, "Los datos de la respuesta son inválidos"),

    // RESPUESTA USUARIO
    RESPUESTA_USUARIO_NOT_FOUND(881, "No se encontró el intento del cuestionario"),
    RESPUESTA_USUARIO_INVALID_DATA(882, "Los datos del intento son inválidos"),
    CUESTIONARIO_YA_COMPLETADO(883, "Este intento de cuestionario ya fue completado"),
    PREGUNTA_YA_RESPONDIDA(884, "Esta pregunta ya fue respondida en este intento"),

    // ENCUESTAS
    ENCUESTA_NOT_FOUND(901, "La encuesta no existe"),
    ENCUESTA_ALREADY_EXISTS(902, "La encuesta ya existe"),
    ENCUESTA_INVALID_DATA(903, "Los datos de la encuesta son inválidos"),
    PREGUNTA_ENCUESTA_NOT_FOUND(911, "La pregunta de encuesta no existe"),
    PREGUNTA_ENCUESTA_INVALID_DATA(912, "Los datos de la pregunta de encuesta son inválidos"),
    OPCION_RESPUESTA_ENCUESTA_NOT_FOUND(921, "La opción de respuesta no existe"),
    RESPUESTA_ENCUESTA_USUARIO_NOT_FOUND(931, "No se encontró la respuesta de encuesta"),
    RESPUESTA_ENCUESTA_USUARIO_INVALID_DATA(932, "Los datos de la respuesta de encuesta son inválidos"),
    RESPUESTA_ENCUESTA_YA_COMPLETADA(933, "Esta respuesta de encuesta ya fue completada"),
    RESPUESTA_ENCUESTA_EN_PROGRESO(934, "Ya existe una respuesta en progreso para este usuario y encuesta"),
    RESPUESTA_ENCUESTA_PREGUNTAS_OBLIGATORIAS(935, "No se han respondido todas las preguntas obligatorias"),
    SEDE_ID_REQUIRED(941, "Se requiere el ID de la sede para encuestas de tipo SEDE"),
    COORDINADOR_ID_REQUIRED(942, "Se requiere el ID del coordinador para encuestas de tipo COORDINACION"),
    USUARIO_ID_REQUIRED(943, "Se requiere el ID del usuario para encuestas de tipo PERSONAL"),
    USUARIO_REQUIRED_NON_ANONYMOUS(944, "Se requiere un usuario para encuestas no anónimas"),
    OPCION_REQUIRED(951, "Se requiere una opción para este tipo de pregunta"),
    TEXTO_REQUIRED(952, "Se requiere un texto para este tipo de pregunta"),
    NUMERO_REQUIRED(953, "Se requiere un número para este tipo de pregunta"),
    FECHA_REQUIRED(954, "Se requiere una fecha para este tipo de pregunta"),
    PREGUNTA_NO_PERTENECE_ENCUESTA(961, "La pregunta no pertenece a la encuesta"),
    OPCION_NO_PERTENECE_PREGUNTA(962, "La opción no pertenece a la pregunta");

    private final Integer errorCode;
    private final String errorMessage;
}
