# Configuración específica para DESARROLLO LOCAL
spring.application.name=crm
server.port=9039
server.address=0.0.0.0

# Configuración de seguridad para desarrollo
server.ssl.enabled=false

# MYSQL LOCALHOST
spring.datasource.url=*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver


#Parametros ThreadPoolTaskExecutor
threadpool.corepoolsize=7
threadpool.maxpoolsize=15
threadpool.queuecapacity=25

# Configuraci�n de JPA para desarrollo
spring.jpa.hibernate.ddl-auto=update